﻿// <copyright file="DebounceManager.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Util
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    /// <summary>
    /// デバウンス処理.
    /// 処理が発生してから指定された期間(Interval)も起きなかったら最後に登録されたイベントを発生させる.
    ///
    /// 監視処理で短期間(例えば１秒間 (実際は Interval で指定された移管))に処理が実行されたとき
    /// 複数回イベントが発生しないようにするための処理.
    /// </summary>
    internal class DebounceManager
    {
        /// <summary>
        /// イベントハンドラをデバウンス期間を取得または設定する.
        /// 処理が発生してから指定された期間(ミリ秒)も起きなかったらイベントが起動する.
        /// </summary>
        public int Interval { get; init; }

        /// <summary>
        /// デバウンス処理を実行する.
        /// </summary>
        /// <param name="action">実際の処理.</param>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        public void Debounce(Action action, CancellationToken cancellationToken)
        {
            // タスク変換する
            Func<Task> taskWrapper = () =>
                Task.Run(() => action.Invoke(), cancellationToken);

            _ = this.DebounceInternal(taskWrapper, cancellationToken);
        }

        /// <summary>
        /// デバウンス処理を生成して、実際の処理を実行する.
        /// </summary>
        /// <param name="taskWrapper">タスク化した実際の処理.</param>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>デバウンス処理.</returns>
        public Task DebounceInternal(Func<Task> taskWrapper, CancellationToken cancellationToken)
        {
            lock (this._debounceLock)
            {
                // タスクをキャプチャ
                this._taskWrapper = taskWrapper;

                // 現在時刻をキャプチャ
                this._lastInvoked = DateTime.Now;

                // 実行中の場合、その処理を返す
                if (this._waitingTask is not null)
                {
                    return this._waitingTask;
                }

                this._waitingTask = Task.Run(
                    async () =>
                    {
                        // 経過時間まで待機する
                        do
                        {
                            var delay = this.Interval - ElapsedMilliseconds();
                            await Task.Delay((int)(delay < 0 ? 0 : delay), cancellationToken);
                        }
                        while (DelayTest());

                        // 処理を起動する
                        try
                        {
                            await this._taskWrapper.Invoke();
                        }
                        finally
                        {
                            lock (this._debounceLock)
                            {
                                this._waitingTask = null;
                            }
                        }
                    }, cancellationToken);

                return this._waitingTask;
            }

            double ElapsedMilliseconds() => (DateTime.Now - this._lastInvoked).TotalMilliseconds;
            bool DelayTest() => ElapsedMilliseconds() < this.Interval;
        }

        /// <summary>
        /// DebounceInternal をロックするための処理.
        /// </summary>
        private readonly object _debounceLock = new ();

        /// <summary>
        /// 待機中の実際の処理.
        /// </summary>
        private Task _waitingTask;

        /// <summary>
        /// タスク化した実際の処理を生成する処理.
        /// </summary>
        private Func<Task> _taskWrapper;

        /// <summary>
        /// 最後に実行した実行日時.
        /// </summary>
        private DateTime _lastInvoked;
    }
}
