﻿// <copyright file="Startup.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Bootstrap
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Constants;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Service;
    using JproDeploymentService.Service;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Hosting.WindowsServices;
    using LifetimeEventService = JproDeploymentService.Common.Service.LifetimeEventService;
    using WindowsServiceLifetimeEventService = JproDeploymentService.Common.Service.WindowsServiceLifetimeEventService;

    /// <summary>
    /// .NETプログラムセットアップ処理クラス.
    /// Windows サービスとして稼働できるように .NET ホストにサービスを登録する.
    /// </summary>
    internal class Startup
    {
        /// <summary>
        /// <see cref="Startup"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="environment">実行レベル.</param>
        public Startup(string environment)
        {
            this.Environment = environment;
        }

        /// <summary>
        /// 実行レベルを取得または設定する.
        /// </summary>
        private string Environment { get; set; }

        /// <summary>
        /// サービス追加処理.
        /// アプリケーションで使用する諸機能をサービスコンテナ(DIコンテナ)へ登録する.
        /// </summary>
        /// <param name="context">ホスト生成用のコンテキスト情報.</param>
        /// <param name="services">.NETホストが管理するサービスコレクション.</param>
        /// <param name="configuration">設定ファイル.</param>
        public void ConfigureServices(
            HostBuilderContext context,
            IServiceCollection services,
            JproReleaseConfiguration configuration)
        {
            // Entity Framewrok についてはコネクションを維持しないため InternalActivator で管理する

            // 特定のJPROライブラリで EnvironmentName (Develoment や Production)を取得するため
            // IWebHostEnvironment の DI を要求しているため疑似的に IWebHostEnvironment を生成する
            var environment = new PseudoWebHostEnvironment
            {
                EnvironmentName = this.Environment,
                HostingEnvironment = context.HostingEnvironment,
            };
            services.AddSingleton<IHostEnvironment>(environment);
            services.AddSingleton<IWebHostEnvironment>(environment);

            // ロガー向けに MessageProvider を設定する
            services.AddSingleton<IMessageProvider, MessageProvider>();

            // イベント終了・開始時のハンドラを登録する
            services.AddHostedService<LifetimeEventService>();

            // Windows サービスとして起動している場合、Windowsサービスライフタイムイベントのハンドラを登録する
            if (WindowsServiceHelpers.IsWindowsService())
            {
                services.AddSingleton<IHostLifetime, WindowsServiceLifetimeEventService>();
            }

            // メインサービスワーカーを登録する
            services.AddHostedService<DeploymentServiceMain>();

            // 正規表現のタイムアウト時間を設定する
            if (configuration.Etc is Etc etc && etc.RegexTimeout > 0)
            {
                Constants.Base.RegexDefaultMatchTimeout = configuration.Etc.RegexTimeout;
            }

            AppDomain.CurrentDomain.SetData(
                Constants.Base.AppDomainKeyRegexDefaultMatchTimeout,
                TimeSpan.FromSeconds(Constants.Base.RegexDefaultMatchTimeout));
        }
    }
}
