trigger:
  branches:
    include:
      - develop/sc/main*-sst2

resources:
  repositories:
    - repository: JPROKouseiKanri
      type: git
      ref: main
      name: JPROKouseiKanri

jobs:
  - job: BuildJproDeploymentService
    displayName: Build JproDeploymentService
    pool: ActiveReports
    timeoutInMinutes: 240
    workspace:
      clean: all
    steps:
      - checkout: self
        path: JproDeploymentService
        fetchDepth: 0
        clean: true
        fetchTags: true

      # Download artifact from a specific build and extract to a target folder
      - task: DownloadPipelineArtifact@2
        displayName: 'Download Artifact JproBackendRepo'
        inputs:
          artifact: '$(artifactName)'
          buildType: 'specific'
          buildVersionToDownload: 'latest'
          project: '$(System.TeamProject)'
          pipeline: 317
          targetPath: '$(Build.SourcesDirectory)'

      - task: PowerShell@2
        name: VerifyArtifacts
        displayName: 'Verify and Extract Artifacts'
        inputs:
          workingDirectory: '$(Build.SourcesDirectory)'
          targetType: 'inline'
          script: |
            # Artifact paths (using Build.SourcesDirectory to build full paths)
            $backendZipPath = "$(Build.SourcesDirectory)\$(backendFile).zip"
            $backendChecksumPath = "$(Build.SourcesDirectory)\$(backendFile).zip.sha256"
            
            # Read and extract the expected checksum from the checksum file (content before the space)
            if (Test-Path -Path $backendChecksumPath) {
              $backendChecksumContent = Get-Content -Path $backendChecksumPath -Raw
              $backendExpectedChecksum = $backendChecksumContent.Split(' ')[0]
            } else {
              throw "Checksum file for backend not found: $backendChecksumPath"
            }
            
            # Calculate SHA256 checksum for the backend zip file
            $backendCalculatedChecksum = Get-FileHash -Path $backendZipPath -Algorithm SHA256 | Select-Object -ExpandProperty Hash
            Write-Output "Backend - Expected Checksum: $backendExpectedChecksum"
            Write-Output "Backend - Calculated Checksum: $backendCalculatedChecksum"
            
            # Verify the checksums (case-insensitive comparison)
            if ($backendCalculatedChecksum.ToUpper() -eq $backendExpectedChecksum.ToUpper()) {
              Write-Output "Backend checksum verification succeeded."
              $backendVerified = $true
            } else {
              Write-Output "Backend checksum verification failed."
              exit -1
            }
            
            # If checksum verification passed, extract the artifact
            if ($backendVerified) { 
              Write-Output "Both checksum verifications succeeded. Extracting artifacts..."
            
              # Define the extraction path; ensure the target folder exists (or is recreated)
              $backendExtractPath = "..\JproBackendRepo\JproBackend\JproBackCommon\bin\Release\net6.0"
              if (Test-Path -Path $backendExtractPath) {
                Remove-Item -Recurse -Force -Path $backendExtractPath
                Write-Output "Deleted existing backend directory at $backendExtractPath"
              }
              New-Item -ItemType Directory -Path $backendExtractPath -Force | Out-Null
              Expand-Archive -Path $backendZipPath -DestinationPath $backendExtractPath -Force
              Write-Output "Backend artifact extracted to $backendExtractPath"
            
              # List files in the extraction folder
              Get-ChildItem -Path $backendExtractPath | ForEach-Object { Write-Output $_.FullName }

              # Set a variable indicating success
              Write-Output "##vso[task.setvariable variable=checksumVerified]true"
            } else {
              Write-Output "Checksum verification failed."
              exit -1
            }

      - template: template-build-msbuild-renew.yml@JPROKouseiKanri
        parameters:
          build: "JproDeploymentService.sln"
          artifactName: "JproDeployment"
          artifactFolder: "JproDeployment"

      - template: template-clean.yml@JPROKouseiKanri
