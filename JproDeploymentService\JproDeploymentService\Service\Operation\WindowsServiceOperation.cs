﻿// <copyright file="WindowsServiceOperation.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service.Operation
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.ServiceProcess;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Cysharp.Diagnostics;
    using JproBackend.Common.Utility.EncryptionUtility;
    using JproBackend.JproBackCommon.Common.Base.Constants;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Utility.FileAccess;
    using JproDeploymentService.Common.Entity;

    /// <summary>
    /// Windowsサービス操作クラス.
    /// Windows サービスのインストールを制御するための処理の実装.
    /// </summary>
    internal class WindowsServiceOperation
    {
        /// <summary>
        /// <see cref="FileOperation"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="logger">ロガー.</param>
        /// <param name="configuration">設定ファイル.</param>
        public WindowsServiceOperation(
            FileLogger<WindowsServiceOperation> logger,
            JproReleaseConfiguration configuration)
        {
            this.Logger = logger;
            this.Configuration = configuration;
        }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<WindowsServiceOperation> Logger { get; }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; }

        /// <summary>
        /// JPROバックエンドサービス登録処理.
        /// JPROバックエンドをサービス登録する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <returns>このメソッドは戻り値のない非同期処理.</returns>
        public async Task RegisterJproService(AppVersion appVersion)
        {
            // サービス名
            var serviceName = $"JproBackend_v{appVersion.ToString("_")}";

            // 表示名
            var displayName = $"JPROバックエンドサービス v{appVersion}";

            // インストール先
            var binPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend", $"v{appVersion}", "JproBackend.exe");

            // ユーザー名
            var user = "<EMAIL>";

            // パスワード
            var password = "Brucelee212820@";

            var command =
                $@"sc create " +
                $@"{serviceName} " +
                $@"displayname= ""{displayName}"" " +
                (string.IsNullOrEmpty(user) ? string.Empty : $@"obj= {user} ") +
                (string.IsNullOrEmpty(password) ? string.Empty : $@"password= {password} ") +
                $@"type= own " +
                $@"start= delayed-auto " +
                $@"binpath= ""{binPath}""";

            await foreach (var result in ProcessX.StartAsync(command))
            {
                this.Logger.DebugLog(result);
            }
        }

        /// <summary>
        /// JPROバックエンドサービス起動処理.
        /// インストールしたサービスを起動する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public void StartService(AppVersion appVersion)
        {
            this.GetService(appVersion)?.Start();
        }

        /// <summary>
        /// JPROバックエンドサービス非活性処理.
        /// インストールしたサービスを再起動時に自動起動しないようにする.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        public async Task DesabledService(AppVersion appVersion)
        {
            // サービス名
            var serviceName = $"JproBackend_v{appVersion.ToString("_")}";

            var command =
                $@"sc config " +
                $@"{serviceName} " +
                $@"start= demand ";

            await foreach (var result in ProcessX.StartAsync(command))
            {
                this.Logger.DebugLog(result);
            }
        }

        /// <summary>
        /// JPROバックエンドサービス削除処理.
        /// インストールしたサービスを停止する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        public async Task DeleteService(AppVersion appVersion)
        {
            // サービス名
            var serviceName = $"JproBackend_v{appVersion.ToString("_")}";

            // 削除コマンドを実行する
            var command = $@"sc delete {serviceName} ";
            await foreach (var result in ProcessX.StartAsync(command))
            {
                this.Logger.DebugLog(result);
            }
        }

        /// <summary>
        /// JPROバックエンドサービス停止処理.
        /// インストールしたサービスを停止する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public void StopService(AppVersion appVersion)
        {
            // サービスを開始ているなら、そのバージョンサービスを停止する
            if (this.GetService(appVersion) is ServiceController serviceController &&
                serviceController.Status == ServiceControllerStatus.Running)
            {
                serviceController.Stop();
            }
        }

        /// <summary>
        /// Webサーバー設定ファイルリロード処理.
        /// Nginx の設定を更新する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <returns>このメソッドは戻り値のない非同期処理.</returns>
        public async Task ReloadNginxConfig(AppVersion appVersion)
        {
            var nginxConfigPath = Path.Combine(
                this.Configuration.Deployment.JproBaseDirectory,
                "Frontend\\nginx");

            MoveNginxConfigPath("jpro-http");
            MoveNginxConfigPath("jpro-https");

            // 設定ファイルをリロードする
            try
            {
                var command = Path.Combine(this.Configuration.Deployment.NginxDirectory, "nginx.exe");
                await foreach (var result in ProcessX.StartAsync($"{command} -s reload", workingDirectory: this.Configuration.Deployment.NginxDirectory))
                {
                    this.Logger.DebugLog(result);
                }
            }
            catch (Exception exception)
            {
                this.Logger.WarningLog("Nginx の設定ファイルリロードでエラーが発生しました.");
                this.Logger.WarningLog(exception.Message);
            }

            void MoveNginxConfigPath(string prefix)
            {
                // 既存の設定ファイルをバックアップする
                File.Copy(
                    Path.Combine(nginxConfigPath, $"{prefix}.nginx.conf"),
                    Path.Combine(nginxConfigPath, $"{prefix}.nginx.{DateTime.Now.ToString("yyyyMMddhhmmssfff")}.conf"),
                    true);

                // 設定ファイルを更新する
                File.Copy(
                    Path.Combine(nginxConfigPath, $"{prefix}.nginx.{appVersion.ToString("_")}.conf"),
                    Path.Combine(nginxConfigPath, $"{prefix}.nginx.conf"),
                    true);

                // ファイルを削除する
                FileAccessor.Delete(Path.Combine(nginxConfigPath, $"{prefix}.nginx.{appVersion.ToString("_")}.conf"));
            }
        }

        /// <summary>
        /// JPROバックエンドのサービス制御機能取得処理.
        /// 指定したバージョンの JPRO バックエンドサービスを取得する.
        /// </summary>
        /// <param name="appVersion">Jバージョン.</param>
        /// <returns>PRO バックエンドサービス.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        private ServiceController GetService(AppVersion appVersion)
        {
            var serviceName = $"JproBackend_v{appVersion.ToString("_")}";

            return ServiceController.GetServices()
                .FirstOrDefault(service => service.ServiceName == serviceName);
        }

        /// <summary>
        /// 暗号化文字列復号処理.
        /// 暗号化されたID/パワードを復号化する.
        /// </summary>
        /// <param name="encryptText">暗号化された文字列.</param>
        /// <returns>復号化された文字列.</returns>
        private string DecryptedText(string encryptText)
        {
            if (string.IsNullOrWhiteSpace(encryptText))
            {
                return null;
            }

            try
            {
                using var memoryStream = new MemoryStream(Convert.FromHexString(encryptText));
                return EncryptionUtility.GetDecryptedText(
                    Constants.Base.ConnectionStringDecryptPassword,
                    memoryStream);
            }
            catch (Exception exception)
            {
                this.Logger.ErrorLog(exception, "パスワードの復号化処理に失敗しました。");
                return null;
            }
        }
    }
}
