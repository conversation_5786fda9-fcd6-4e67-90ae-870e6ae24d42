﻿// <copyright file="PseudoWebHostEnvironment.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Service
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.FileProviders;
    using Microsoft.Extensions.Hosting;

    /// <summary>
    /// 特定のJPROライブラリで EnvironmentName (Develoment や Production)を取得するため
    /// IWebHostEnvironment の DI を要求しているクラスのために疑似的な IWebHostEnvironment を補完するクラス.
    /// </summary>
    internal class PseudoWebHostEnvironment : IWebHostEnvironment
    {
        /// <summary>
        /// 環境の名前を取得または設定する.
        /// </summary>
        public string EnvironmentName { get; set; }

        /// <summary>
        /// アプリケーションの名前を取得または設定する.
        /// </summary>
        public string ApplicationName
        {
            get => this.HostingEnvironment.ApplicationName;
            set => this.HostingEnvironment.ApplicationName = value;
        }

        /// <summary>
        /// アプリケーションのコンテンツ ファイルを含むディレクトリへの絶対パスを取得または設定する.
        /// </summary>
        public string ContentRootPath
        {
            get => this.HostingEnvironment.ContentRootPath;
            set => this.HostingEnvironment.ContentRootPath = value;
        }

        /// <summary>
        /// ContentRootPath をポイントする IFileProvider を取得または設定する.
        /// </summary>
        public IFileProvider ContentRootFileProvider
        {
            get => this.HostingEnvironment.ContentRootFileProvider;
            set => this.HostingEnvironment.ContentRootFileProvider = value;
        }

        /// <summary>
        /// Web 予約可能なアプリケーション コンテンツ ファイルを含むディレクトリへの絶対パスを取得または設定する.
        /// </summary>
        public string WebRootPath
        {
            get => throw new NotImplementedException();
            set => throw new NotImplementedException();
        }

        /// <summary>
        /// WebRootPath をポイントする IFileProvider を取得または設定する.
        /// </summary>
        public IFileProvider WebRootFileProvider
        {
            get => throw new NotImplementedException();
            set => throw new NotImplementedException();
        }

        /// <summary>
        /// ワーカーホストとしての環境設定を取得または設定する.
        /// </summary>
        internal IHostEnvironment HostingEnvironment { get; set; }
    }
}
