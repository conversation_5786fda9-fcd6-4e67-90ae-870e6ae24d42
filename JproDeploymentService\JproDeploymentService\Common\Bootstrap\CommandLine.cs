﻿// <copyright file="CommandLine.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Bootstrap
{
    using CommandLineParser = global::CommandLine;

    /// <summary>
    /// コマンドライン引数の解析処理.
    /// </summary>
    internal static class CommandLine
    {
        /// <summary>
        /// コマンドライン引数を解析する.
        /// </summary>
        /// <param name="args">エントリポイントに指定されたコマンドライン引数.</param>
        /// <returns>解析されたコマンドライン引数情報.</returns>
        public static OptionData ParseCommandLine(string[] args)
        {
            var parser = new CommandLineParser.Parser();
            var parseResult = parser.ParseArguments<OptionData>(args) as CommandLineParser.Parsed<OptionData>;
            return parseResult.Value;
        }
    }

    /// <summary>
    /// 解析されたコマンドライン引数情報.
    /// </summary>
    internal class OptionData
    {
        /// <summary>
        /// インストールモードであるか判別するためのフラグ.
        /// インストールモードの場合 Windows サービスとして稼働せず、JproDevelopmentService 自身を Windows にインストールする特別な処理を行う.
        /// true の場合、インストールモードである. false の場合、通常モードである.
        /// </summary>
        [CommandLineParser.Option('i', "install", Required = false, Default = false, HelpText = "インストールモードとして稼働する")]
        public bool InstallMode { get; set; }

        /// <summary>
        /// 開発モードであるか判別するためのフラグ.
        /// 開発モードの場合、設定ファイルを Development モードで参照する.
        /// true の場合、開発モードである. false の場合、通常モードである.
        /// </summary>
        [CommandLineParser.Option('d', "development", Required = false, Default = false, HelpText = "開発モードとして稼働する")]
        public bool DevelopmentMode { get; set; }
    }
}
