﻿// <copyright file="DbWatcher.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Channels;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Dao.TableExtension;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;
    using Microsoft.Data.SqlClient;

    /// <summary>
    /// DB変更監視クラス.
    /// データベース監視処理.
    /// </summary>
    internal sealed class DbWatcher : IDisposable
    {
        /// <summary>
        /// ポーリングモードの場合の、ポーリング間隔.
        /// </summary>
        public const int PollingIntervalSecond = 15;

        /// <summary>
        /// <see cref="DbChangeWatcher"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="interval">デバウンスの待機期間(ミリ秒).</param>
        /// <param name="logger">ロガー.</param>
        /// <param name="watcherLogger">テーブル監視用ロガー.</param>
        /// <param name="configuration">設定ファイル.</param>
        public DbWatcher(
            int interval,
            FileLogger<DbWatcher> logger,
            FileLogger<DbTableWatcher> watcherLogger,
            JproReleaseConfiguration configuration)
        {
            this.Logger = logger;
            this.WatcherLogger = watcherLogger;
            this.Configuration = configuration;

            // 変更検知モードが未設定の場合、更新しない
            this.CacheMode = this.Configuration.Etc?.StaticCache ?? "Polling";

            // デバウンス処理
            this.DebounceManager = new () { Interval = interval };

            // イベントキュー
            this.DbChangeActionQueue = Channel.CreateUnbounded<DbChangeEventArg>(new UnboundedChannelOptions
            {
                SingleReader = true,
            });
        }

        /// <summary>
        /// リソース解放機能処理.
        /// テーブル監視機能を解放する.
        /// </summary>
        public void Dispose()
        {
            // 内部ループ向けに停止フラグを有効化する
            this.IsDisposing = true;

            // null を設定すると外部へのイベントが発生しなくなる
            this.DbChangeAction = null;

            // クエリ通知モードの場合、クエリ通知を終了する
            if (this.IsSqlDependency())
            {
                SqlDependency.Stop(this.Configuration.Database.ChozaiDbConnectionString);
            }
        }

        /// <summary>
        /// データベースが更新されたときのイベントハンドラを取得または設定する.
        /// </summary>
        public Func<DbChangeEventArg, List<AppVersionControl>> DbChangeAction { get; set; } = null;

        /// <summary>
        /// データベース監視処理.
        /// データベースを監視する.
        /// </summary>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>読み取りされたアプリバージョン.</returns>
        public Task<List<AppVersionControl>> ObserveTables(CancellationToken cancellationToken)
        {
            // クエリ通知モードの場合、クエリ通知を開始する
            if (this.IsSqlDependency())
            {
                try
                {
                    SqlDependency.Start(this.Configuration.Database.ChozaiDbConnectionString);
                }
                catch (Exception exception)
                {
                    this.Logger.ErrorLog(exception, "DB監視のクエリ通知(SqlDependency)モード開始に失敗しました。以降、ポーリングモードで対応します。");
                    this.CacheMode = "Polling";
                }
            }

            // キャンセルトークンを設定
            this.CancellationToken = cancellationToken;

            // DB監視処理を初期化する.
            Func<SqlConnection> connectionGenerator = this.CreateConnectionGenerator();
            this.InitializeDbWatcher(cancellationToken, connectionGenerator);

            // クエリ通知モードの場合、データ監視を実施する
            // ポーリングの場合も、データ読み込みを行う
            using SqlConnection connection = connectionGenerator();
            this.DbWatchers.ForEach(watcher => watcher.UpdateDbCache(connection));

            // ポーリングの場合、変更検知のためのダイジェストを生成する
            if (this.IsPolling())
            {
                this.DbWatchers
                    .ToList()
                    .ForEach(watcher => watcher.PollingDigest(connection));
            }

            // ポーリングモードの場合、ポーリングを開始
            if (this.IsPolling())
            {
                Task.Run(() => this.PollingTables());
            }

            // キューの監視を開始
            return this.ExecuteMainLoop();
        }

        /// <summary>
        /// テーブル変更監視機能の初期化処理.
        /// DB監視処理を初期化する.
        /// </summary>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <param name="connectionGenerator">コネクション生成処理.</param>
        private void InitializeDbWatcher(CancellationToken cancellationToken, Func<SqlConnection> connectionGenerator)
        {
            // システム店舗情報マスタ
            var sysShopIniWatcher = new DbTableWatcher
            {
                FetchQuery = @"
    SELECT [DATA_DIV]
      ,[START_DATE]
      ,[LAST_DATE]
      ,[ESTABLISH_NAME]
      ,[FIELD_NAME]
      ,[CHAR_VALUE]
      ,[REAL_VALUE]
      ,[INT_VALUE]
      ,[FLAG_VALUE]
      ,[DATETIME_VALUE]
      ,[MONEY_VALUE]
    FROM [dbo].[SYS_SHOP_INI]",
                PollingQuery = @"
    SELECT MAX([ID]) FROM [dbo].[SYS_SHOP_INI_JPRO_HIS] ",
                Logger = this.WatcherLogger,
                TablePhysicalLogName = "SYS_SHOP_INI",
                ConnectionGenerator = connectionGenerator,
                CacheMode = this.CacheMode,
                OnFetch = reader =>
                {
                    DbStaticCache.SysShopIni = reader.Cast<IDataRecord>()
                        .Select(record => new SysShopIni
                        {
                            DataDiv = record.GetInt16(0),
                            StartDate = record.GetDateTime(1),
                            LastDate = record.GetDateTime(2),
                            EstablishName = record.GetString(3),
                            FieldName = record.GetString(4),
                            CharValue = record.GetNullOrString(5),
                            RealValue = record.GetNullOrValue(6, record.GetFloat),
                            IntValue = record.GetNullOrValue(7, record.GetInt32),
                            FlagValue = record.GetNullOrValue(8, record.GetByte),
                            DatetimeValue = record.GetNullOrValue(9, record.GetDateTime),
                            MoneyValue = record.GetNullOrValue(10, record.GetDecimal),
                        })
                        .ToList();

                    this.Logger.DebugLog("DB監視機能によってシステム店舗情報マスタ情報(SYS_SHOP_INI)を更新しました");
                },
            };

            // システム共通情報マスタ
            var sysCommonIniWatcher = new DbTableWatcher
            {
                FetchQuery = @"
    SELECT [DATA_DIV]
          ,[START_DATE]
          ,[LAST_DATE]
          ,[ESTABLISH_NAME]
          ,[FIELD_NAME]
          ,[CHAR_VALUE]
          ,[REAL_VALUE]
          ,[INT_VALUE]
          ,[FLAG_VALUE]
          ,[DATETIME_VALUE]
          ,[MONEY_VALUE]
     FROM [dbo].[SYS_COMMON_INI]",
                PollingQuery = @"
    SELECT MAX([ID]) FROM [dbo].[SYS_COMMON_INI_JPRO_HIS] ",
                Logger = this.WatcherLogger,
                TablePhysicalLogName = "SYS_COMMON_INI",
                ConnectionGenerator = connectionGenerator,
                CacheMode = this.CacheMode,
                OnFetch = reader =>
                {
                    DbStaticCache.SysCommonIni = reader.Cast<IDataRecord>()
                        .Select(record => new SysCommonIni
                        {
                            DataDiv = record.GetInt16(0),
                            StartDate = record.GetDateTime(1),
                            LastDate = record.GetDateTime(2),
                            EstablishName = record.GetString(3),
                            FieldName = record.GetString(4),
                            CharValue = record.GetNullOrString(5),
                            RealValue = record.GetNullOrValue(6, record.GetFloat),
                            IntValue = record.GetNullOrValue(7, record.GetInt32),
                            FlagValue = record.GetNullOrValue(8, record.GetByte),
                            DatetimeValue = record.GetNullOrValue(9, record.GetDateTime),
                            MoneyValue = record.GetNullOrValue(10, record.GetDecimal),
                        })
                        .ToList();

                    this.Logger.DebugLog("DB監視機能によってシステム共通情報マスタ情報(SYS_COMMON_INI)を更新しました");
                },
            };

            // システム共通情報マスタ
            var appVersionControlWatcher = new DbTableWatcher
            {
                FetchQuery = @"
    SELECT
        [APP_CODE]
        , [MAJOR_NUM]
        , [MINOR_NUM]
        , [BUILD_NUM]
        , [REVISION_NUM]
        , [START_DATE]
        , [LAST_DATE]
        , [CREATE_DATE]
        , [UPDATE_DATE]
    FROM
        [dbo].[APP_VERSION_CONTROL] 
    WHERE
        [APP_CODE] = 5",
                PollingQuery = @"
    SELECT MAX([ID]) FROM [dbo].[APP_VERSION_CONTROL_JPRO_HIS] ",
                Logger = this.WatcherLogger,
                TablePhysicalLogName = "APP_VERSION_CONTROL",
                ConnectionGenerator = connectionGenerator,
                CacheMode = this.CacheMode,
                OnFetch = reader =>
                {
                    var appVersionControl = reader.Cast<IDataRecord>()
                        .Select(record => new AppVersionControl
                        {
                            AppCode = record.GetInt16(0),
                            MajorNum = record.GetInt16(1),
                            MinorNum = record.GetInt16(2),
                            BuildNum = record.GetInt16(3),
                            RevisionNum = record.GetInt16(4),
                            StartDate = record.GetDateTime(5),
                            LastDate = record.GetDateTime(6),
                            CreateDate = record.GetNullOrValue(7, record.GetDateTime),
                            UpdateDate = record.GetNullOrValue(8, record.GetDateTime),
                        })
                        .ToList();

                    // イベントがアサインされていない場合、追加のログを表示しないようにキュー登録しない
                    if (this.DbChangeAction is null)
                    {
                        return;
                    }

                    // 短期間に複数回の変更検知が発生した場合に、一つのイベントにまとめて処理させる
                    this.DebounceManager.Debounce(
                        () =>
                        {
                            // キュー登録する
                            this.DbChangeActionQueue.Writer.WriteAsync(new DbChangeEventArg
                            {
                                Id = Guid.NewGuid().ToString().Replace("-", string.Empty),
                                EventTime = DateTime.Now,
                                Parameters = appVersionControl,
                                CancellationToken = cancellationToken,
                            });
                        }, cancellationToken);
                },
                OnUpdate = self =>
                {
                    // イベントがアサインされていない場合、クエリ通知を再発行しない
                    if (this.DbChangeAction is null)
                    {
                        return;
                    }

                    // データがコード値データ格納先マスタテーブルで有効なら、キャッシュ最新化処理を呼び出だす.
                    SqlConnection connection = null;
                    try
                    {
                        connection = self.ConnectionGenerator();
                        self.UpdateDbCache(connection);
                    }
                    catch (Exception exception)
                    {
                        this.Logger.ErrorLog(exception, $"{self.TablePhysicalLogName} の更新中にエラーが発生しました.");
                    }
                    finally
                    {
                        connection?.Close();
                    }
                },
            };

            this.DbWatchers.AddRange(new[]
            {
                sysShopIniWatcher,
                sysCommonIniWatcher,
                appVersionControlWatcher,
            });
        }

        /// <summary>
        /// メインループ処理.
        /// キューを監視して、フォルダが更新されたときのイベントハンドラを実行する.
        /// </summary>
        /// <returns>読み取りされたアプリバージョン.</returns>
        private async Task<List<AppVersionControl>> ExecuteMainLoop()
        {
            this.Logger.DebugLog($"DB監視処理のイベントキューを開始します。");

            while (await this.DbChangeActionQueue.Reader.WaitToReadAsync(this.CancellationToken) &&
                !this.CancellationToken.IsCancellationRequested &&
                !this.IsDisposing)
            {
                this.Logger.DebugLog($"DB更新のイベント処理を実行します。");

                // キューを読み出す
                var eventArgs = await this.DbChangeActionQueue
                    .Reader
                    .ReadAsync(this.CancellationToken);

                // 同期でアクションを実行する
                if (this.DbChangeAction?.Invoke(eventArgs) is List<AppVersionControl> version)
                {
                    return version;
                }
                else
                {
                    this.Logger.DebugLog($"次のDB更新まで再度待機します。");
                }
            }

            this.Logger.DebugLog($"DB監視処理のイベントキューを終了します。");
            return null;
        }

        /// <summary>
        /// ポーリング監視処理.
        /// 静的データキャッシュの更新確認を行い更新が行われている場合、再読み込みを行う.
        /// </summary>
        /// <returns>本処理は非同期タスク</returns>
        private async Task PollingTables()
        {
            this.Logger.DebugLog($"DBポーリング用の監視ループを開始しました。");

            while (!this.CancellationToken.IsCancellationRequested && !this.IsDisposing)
            {
                // 15秒間待機
                await Task.Delay(PollingIntervalSecond * 1000, this.CancellationToken);

                SqlConnection connection = null;
                try
                {
                    connection = this.CreateConnectionGenerator()();
                    foreach (var watcher in this.DbWatchers)
                    {
                        if (watcher.PollingDigest(connection))
                        {
                            this.Logger.DebugLog($"{watcher.TablePhysicalLogName} の更新を検知しました。");
                            watcher.UpdateDbCache(connection);
                        }
                    }
                }
                catch (Exception exception)
                {
                    this.Logger.ErrorLog(exception, "DBポーリング中にエラーが発生しました");
                }
                finally
                {
                    connection?.Dispose();
                }
            }

            this.Logger.DebugLog($"DBポーリング用の監視ループを終了しました。");
        }

        /// <summary>
        /// DBコネクション構築処理の生成処理.
        /// DbTableWatcher で新たにコネクションを生成するためのロジックを生成する.
        /// </summary>
        /// <returns>コネクション生成ロジック.</returns>
        private Func<SqlConnection> CreateConnectionGenerator()
        {
            // コネクション生成処理
            return () =>
            {
                var connection = new SqlConnection(this.Configuration.Database.ChozaiDbConnectionString);
                connection.Open();
                return connection;
            };
        }

        /// <summary>
        /// キャンセルトークン.
        /// </summary>
        private CancellationToken CancellationToken { get; set; }

        /// <summary>
        /// オブジェクトが破棄済みかどうか判別するためのフラグ.
        /// true の場合、破棄済みである. false の場合、破棄済みでない.
        /// </summary>
        private bool IsDisposing { get; set; }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<DbWatcher> Logger { get; }

        /// <summary>
        /// 監視クラス用のロガーを取得する.
        /// </summary>
        private FileLogger<DbTableWatcher> WatcherLogger { get; }

        /// <summary>
        /// ワーカーの管理クラスを取得する.
        /// </summary>
        private Channel<DbChangeEventArg> DbChangeActionQueue { get; set; }

        /// <summary>
        /// デバウンス処理を取得する.
        /// </summary>
        private DebounceManager DebounceManager { get; }

        /// <summary>
        /// 静的なデータベースクエリキャッシュ.
        /// Polling: ポーリング処理
        /// SqlDependency: クエリ通知.
        /// </summary>
        private string CacheMode { get; set; }

        /// <summary>
        /// 固定で監視を行うテーブル一覧.
        /// </summary>
        private List<DbTableWatcher> DbWatchers { get; } = new ();

        /// <summary>
        /// 設定ファイルの検知種別がポーリングモードかを確認する.
        /// </summary>
        /// <returns>true の場合、ポーリングモード. false の場合、 ポーリングモード以外.</returns>
        private bool IsPolling() => this.CacheMode == "Polling";

        /// <summary>
        /// 設定ファイルの検知種別がクエリ通知(SqlDependency)モードかを確認する.
        /// </summary>
        /// <returns>true の場合、クエリ通知モード. false の場合、 クエリ通知モード以外.</returns>
        private bool IsSqlDependency() => this.CacheMode == "SqlDependency";
    }

    /// <summary>
    /// 単一のDB更新処理イベントの引数.
    /// </summary>
    internal class DbChangeEventArg
    {
        /// <summary>
        /// イベント日時を取得または設定する.
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// イベントIDを取得または設定する.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// ファイル監視機能のイベント情報を取得または設定する.
        /// </summary>
        public List<AppVersionControl> Parameters { get; set; }

        /// <summary>
        /// キャンセルトークンを取得または設定する.
        /// </summary>
        public CancellationToken CancellationToken { get; set; }
    }
}
