﻿// <copyright file="JproReleaseConfiguration.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Entity
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Utility.ConnectionStringUtility;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Primitives;

    /// <summary>
    /// JPROリリース機能の設定ファイル情報.
    /// </summary>
    public class JproReleaseConfiguration : IConfiguration
    {
        /// <summary>
        /// データベース設定.
        /// </summary>
        public Database Database { get; set; }

        /// <summary>
        /// インストール処理設定.
        /// </summary>
        public Deployment Deployment { get; set; }

        /// <summary>
        /// サーバー固有設定情報.
        /// </summary>
        public Servers Servers { get; set; }

        /// <summary>
        /// その他の設定.
        /// </summary>
        public Etc Etc { get; set; }

        /// <summary>
        /// キーを指定して特定の設定セクションの設定値を設定または参照する.
        /// 型付されていない業務機能固有の設定値を参照するために提供している.
        /// </summary>
        /// <param name="key">キー.</param>
        /// <returns>設定値.</returns>
        public string this[string key]
        {
            get => this.InnerConfiguration?[key];
            set
            {
                if (this.InnerConfiguration is not null)
                {
                    this.InnerConfiguration[key] = value;
                }
            }
        }

        /// <summary>
        /// キーを指定して特定の設定セクションを参照する.
        /// </summary>
        /// <param name="key">キー.</param>
        /// <returns>キーに対応する設定セクション.</returns>
        public IConfigurationSection GetSection(string key)
        {
            return this.InnerConfiguration?.GetSection(key);
        }

        /// <summary>
        /// 設定ファイルのルート要素の直下の設定セクションを取得する.
        /// この定義は IConfiguration を実装するために定義している.
        /// </summary>
        /// <returns>ルート要素の直下の設定セクション.</returns>
        public IEnumerable<IConfigurationSection> GetChildren()
        {
            return this.InnerConfiguration?.GetChildren();
        }

        /// <summary>
        /// 設定が変更された場合に監視を行うためのトークンを取得する
        /// 通常、WEBリクエストを処理するたびに最新の設定ファイル定義がコントローラー・業務ロジック・DAOに設定されるので
        /// この更新トークンを個別の画面を実装するためのロジックで参照する必要はない.
        /// この定義は IConfiguration を実装するために定義している.
        /// </summary>
        /// <returns>設定が変更された場合に監視を行うためのトークン.</returns>
        public IChangeToken GetReloadToken()
        {
            return this.InnerConfiguration?.GetReloadToken();
        }

        /// <summary>
        /// 内部的に設定された設定ファイル定義を取得または設定する.
        /// </summary>
        internal IConfiguration InnerConfiguration { get; set; }
    }

    /// <summary>
    /// データベース設定.
    /// </summary>
    public class Database
    {
        /// <summary>
        /// 店舗内データベースの接続文字列.
        /// </summary>
        public string ChozaiDbConnectionString { get; set; }

        /// <summary>
        /// ユーザー名/パスワードが復号化される前の店舗内データベースの接続文字列.
        /// この項目は設定ファイルより設定されているのではなく、ConfigurationExtension のロジックで設定される.
        /// </summary>
        public string BearChozaiDbConnectionString { get; set; }
    }

    /// <summary>
    /// インストール更新処理設定.
    /// この設定は起動時にログに出力する.
    /// </summary>
    public class Deployment
    {
        /// <summary>
        /// リリースファイル展開先ディレクトリ.
        /// </summary>
        public string SharedDirectory { get; set; }

        /// <summary>
        /// JPRO本体のリリース先ディレクトリ.
        /// </summary>
        public string JproBaseDirectory { get; set; }

        /// <summary>
        /// JPRO本体のリリース先ディレクトリ.
        /// </summary>
        public string TempDirectory { get; set; }

        /// <summary>
        /// エラーでインストール処理が失敗した場合に再実行するまでの待機期間.
        /// </summary>
        public int ErrorRetryInterval { get; set; }

        /// <summary>
        /// Nginx のインストールディレクトリ.
        /// </summary>
        public string NginxDirectory { get; set; }

        /// <summary>
        /// リリース結果ファイルの配置先ディレクトリ.
        /// </summary>
        public string DeploymentResultDirectory { get; set; }
    }

    /// <summary>
    /// サーバー固有設定情報.
    /// </summary>
    public class Servers
    {
        /// <summary>
        /// 指定された店舗のサーバー固有設定情報を取得する.
        /// </summary>
        /// <param name="serverName">サーバーホスト名.</param>
        /// <returns>サーバー固有の設定情報.</returns>
        public JproReleaseConfiguration this[string serverName]
        {
            get
            {
                var jproConfig = this.RootSetting?
                    .GetSection($"JproRelease:Servers:{serverName}")?
                    .Get<JproReleaseConfiguration>();

                return jproConfig.InitializeConfiguration(this.RootSetting);
            }
        }

        /// <summary>
        /// サーバー固有設定情報が紐づく親の設定ファイル情報.
        /// </summary>
        internal IConfiguration RootSetting { get; set; }
    }

    /// <summary>
    /// その他の設定.
    /// 内部的な軽微な設定.
    /// </summary>
    public class Etc
    {
        /// <summary>
        /// 正規表現のデフォルトタイムアウト秒.
        /// </summary>
        public int RegexTimeout { get; set; } = ReleaseConstants.RegexTimeoutEtcSetting;

        /// <summary>
        /// サービス終了時のタイムアウト秒.
        /// </summary>
        public int FinalizeTimeout { get; set; } = ReleaseConstants.FinalizeTimeoutEtcSetting;

        /// <summary>
        /// 静的なデータベースクエリキャッシュの保持方式.
        /// Polling: ポーリング処理
        /// SqlDependency: クエリ通知(SqlDependency).
        /// </summary>
        public string StaticCache { get; set; } = ReleaseConstants.StaticCacheEtcSetting;

        /// <summary>
        /// データベース(APP_VERSION_CONTRORL)が更新されたときのイベント発生猶予期間(秒).
        /// 猶予期間中に発生した修正は一つのイベントキューとして処理される.
        /// </summary>
        public int DbDebounceInterval { get; set; } = ReleaseConstants.DbDebounceIntervalEtcSetting;

        /// <summary>
        /// 配信先フォルダが更新されたときのイベント発生猶予期間(秒).
        /// 猶予期間中に発生した修正は一つのイベントキューとして処理される.
        /// </summary>
        public int FileDebounceInterval { get; set; } = ReleaseConstants.FileDebounceIntervalEtcSetting;

        /// <summary>
        /// 空きポートチェック処理のリトライ回数.
        /// </summary>
        public int UnusedPortRetryCount { get; set; } = ReleaseConstants.UnusedPortRetryCountEtcSetting;

        /// <summary>
        /// 空きポートチェック処理のリトライ時間(秒).
        /// </summary>
        public int UnusedPortRetryInterval { get; set; } = ReleaseConstants.UnusedPortRetryIntervalEtcSetting;

        /// <summary>
        /// 自己インストール機能の遅延開始時刻(秒).
        /// </summary>
        public int SeflInstallTaskDelay { get; set; } = ReleaseConstants.SeflInstallTaskDelayEtcSetting;

        /// <summary>
        /// インストールタスク時に失敗したテンポラリのファイルを保持する.
        /// true の場合、保持する. false　の場合、保持しない.
        /// </summary>
        public bool PreserveMaterials { get; set; } = ReleaseConstants.PreserveMaterialsEtcSetting;

        /// <summary>
        /// ファイル監視のリトライ回数.
        /// </summary>
        public int FileRetryCount { get; set; } = ReleaseConstants.FileRetryCountEtcSetting;

        /// <summary>
        /// ファイル監視のリトライ初期間隔(秒).
        /// </summary>
        public int FileRetryInitialInterval { get; set; } = ReleaseConstants.FileRetryInitialIntervalEtcSetting;

        /// <summary>
        /// ファイル監視のリトライ最大間隔(秒).
        /// </summary>
        public int FileRetryMaxInterval { get; set; } = ReleaseConstants.FileRetryMaxIntervalEtcSetting;
    }

    /// <summary>
    /// キー/値形式の設定情報に JPRO 設定情報連携機能を追加する.
    /// </summary>
    internal static class ConfigurationExtension
    {
        /// <summary>
        ///  キー/値形式の設定情報を JPRO の型付け設定情報に変換する.
        /// </summary>
        /// <param name="configuration">キー/値形式の設定情報.</param>
        /// <returns>JPRO の型付け設定.</returns>
        public static JproReleaseConfiguration ToJproReleaseConfiguration(this IConfiguration configuration)
        {
            var jproConfiguration = configuration
               .GetSection("JproRelease")
               .Get<JproReleaseConfiguration>();

            return jproConfiguration.InitializeConfiguration(configuration);
        }

        /// <summary>
        /// JPRO の型付け設定情報を初期化する.
        /// </summary>
        /// <param name="jproConfiguration">PRO の型付け設定.<</param>
        /// <param name="configuration">キー/値形式の設定情報.</param>
        /// <returns>初期化されたPRO の型付け設定.<</returns>
        public static JproReleaseConfiguration InitializeConfiguration(this JproReleaseConfiguration jproConfiguration, IConfiguration configuration)
        {
            // データ存在しない場合、空の設定オブジェクトを生成する
            if (jproConfiguration is null)
            {
                return new JproReleaseConfiguration();
            }

            jproConfiguration.InnerConfiguration = configuration;
            jproConfiguration.Servers = jproConfiguration.Servers ?? new Servers();
            jproConfiguration.Servers.RootSetting = configuration;

            // データベース定義がなければ、そのまま返却する.
            if (jproConfiguration.Database is null)
            {
                return jproConfiguration;
            }

            // 復号化される前の店舗内データベースを取得する
            jproConfiguration.Database.BearChozaiDbConnectionString = jproConfiguration.Database.ChozaiDbConnectionString;

            // 暗号化項目を復号化する
            if (!string.IsNullOrWhiteSpace(jproConfiguration.Database.BearChozaiDbConnectionString))
            {
                jproConfiguration.Database.ChozaiDbConnectionString =
                    ConnectionStringUtility.ToConnectionStringBuilder(jproConfiguration.Database.BearChozaiDbConnectionString).ToString();
            }

            return jproConfiguration;
        }
    }
}
