﻿// <copyright file="WindowsServiceLifetimeEventService.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Service
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Hosting.WindowsServices;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;

    /// <summary>
    /// Windowsサービスライフサイクル管理クラス.
    /// Windows サービスとしてのライフタイムイベントに対応して JPRO リリース機能として必要な処理を行う.
    /// .NET アプリケーションサーバー(.NET ホスト)としてのイベントハンドリングは LifetimeEventService が担当する.
    /// このサービスは Windows サービスとして稼働しているとき以外は動作しない (JproDeploymentService.exe を直接起動しているときは動作しない).
    /// </summary>
    internal class WindowsServiceLifetimeEventService : WindowsServiceLifetime
    {
        /// <summary>
        /// <see cref="WindowsServiceLifetimeEventService"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="environment">アプリケーションが実行されているホスティング環境に関する情報を提供します.</param>
        /// <param name="applicationLifetime">コンシューマーにアプリケーションの有効期間イベントを通知できるようにします.</param>
        /// <param name="loggerFactory">ログ システムを構成し、登録されている ILoggerProvider から ILogger のインスタンスを作成するために使用される型を表します.</param>
        /// <param name="optionsAccessor">構成されたホストオプション取得に使用されます.</param>
        /// <param name="windowsServiceOptionsAccessor">構成されたWindowsServiceオプション取得に使用されます.</param>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "Windows サービスのための対応なので移植性の問題は考慮しない.")]
        public WindowsServiceLifetimeEventService(
            IHostEnvironment environment,
            IHostApplicationLifetime applicationLifetime,
            ILoggerFactory loggerFactory,
            IOptions<HostOptions> optionsAccessor,
            IOptions<WindowsServiceLifetimeOptions> windowsServiceOptionsAccessor)
            : base(environment, applicationLifetime, loggerFactory, optionsAccessor, windowsServiceOptionsAccessor)
        {
            // イベントログの出力を抑止する
            this.AutoLog = false;
        }

        /// <summary>
        /// サービス開始イベントハンドラ.
        /// Windows よりサービス開始された場合のイベントハンドラ.
        /// </summary>
        /// <param name="args">起動時の引数.</param>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "Windows サービスのための対応なので移植性の問題は考慮しない.")]
        protected override void OnStart(string[] args)
        {
            base.OnStart(args);
        }

        /// <summary>
        /// サービス停止イベントハンドラ.
        /// Windows よりサービス停止された場合のイベントハンドラ.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "Windows サービスのための対応なので移植性の問題は考慮しない.")]
        protected override void OnStop()
        {
            if (this.ExitCode == 0 && Environment.ExitCode != 0)
            {
                this.ExitCode = Environment.ExitCode;
            }

            base.OnStop();
        }
    }
}
