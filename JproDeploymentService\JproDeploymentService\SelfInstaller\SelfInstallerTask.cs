﻿// <copyright file="SelfInstallerTask.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.SelfInstaller
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Cysharp.Diagnostics;
    using JproBackend.Common.Utility.ArchiveUtility;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproBackend.JproBackCommon.Common.Utility.FileAccess;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;
    using JproDeploymentService.Service;
    using JproDeploymentService.Service.Operation;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// 自己インストールタスククラス.
    /// 自己インストール作業を制御するための管理クラス.
    /// </summary>
    internal sealed class SelfInstallerTask : IDisposable
    {
        /// <summary>
        /// <see cref="SelfInstallerTask"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="loggerFactory">ロガーファクトリ.</param>
        /// <param name="environment">環境データ.</param>
        /// <param name="messageProvider">メッセージ情報取得処理.</param>
        public SelfInstallerTask(
            ILoggerFactory loggerFactory,
            IHostEnvironment environment,
            IMessageProvider messageProvider)
        {
            this.LoggerFactory = loggerFactory;
            this.Logger = this.SupplyLogger<SelfInstallerTask>();

            this.Configuration = InternalActivator.CreateConfiguration(environment.EnvironmentName, false)
                .ToJproReleaseConfiguration();

            this.MessageProvider = messageProvider;
        }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private ILoggerFactory LoggerFactory { get; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<SelfInstallerTask> Logger { get; }

        /// <summary>
        /// メッセージ取得クラスを取得する.
        /// </summary>
        private IMessageProvider MessageProvider { get; }

        /// <summary>
        /// フォルダ監視処理を取得または設定する.
        /// </summary>
        private FileWatcher FileWatcher { get; set; }

        /// <summary>
        /// 1回分のインストール作業のラウンドトリップのキャンセルトークン.
        /// DeploymentServiceMain がキャンセル指示を実行する.
        /// </summary>
        private CancellationToken CancelToken { get; set; }

        /// <summary>
        /// JPROのインストールタスクとJRPOリリース機能の自己インストールタスクを排他制御するためのシグナル.
        /// </summary>
        private SemaphoreSlim InstallSignal { get; set; }

        /// <summary>
        /// 自己インストール処理.
        /// 1回分のインストール作業を実行する.
        /// </summary>
        /// <param name="cancelToken">インストール処理を停止するためのトークン.</param>
        /// <param name="installSignal">インストールタスクの排他制御シグナル.</param>
        /// <returns>この関数は戻り値のない非同期処理.</returns>
        public async Task Execute(CancellationToken cancelToken, SemaphoreSlim installSignal)
        {
            // installSignal は実際にデータとファイルが準備されてインストール処理が開始してからプロパティでキャプチャする
            this.CancelToken = cancelToken;

            // ファイル監視開始
            string workDirPath = await this.ObserveFileUpdateQueue();

            // ファイルの監視を解放する
            this.ReleaseFileWatcher();

            // キャンセル終了した場合、終了
            if (cancelToken.IsCancellationRequested || workDirPath is null)
            {
                return;
            }

            // インストール準備が完了
            this.Logger.InformationLog(
                $"JPROリリース機能のリリース対象の資材ファイルを検知しました。リリース作業用フォルダは {workDirPath} です。");

            // ここからJPROのインストール機能へのロックを開始する
            // ループの頻度を下げるために10秒間待機している
            if (!installSignal.Wait(10 * 1000, cancelToken))
            {
                this.Logger.InformationLog("JPROのインストール処理を実行中のためJPROリリース機能の自己インストール処理を中断します。");
                return;
            }

            // ロックできたら、プロパティにキャプチャして Dispose 時にリリースする
            this.InstallSignal = installSignal;

            // 配置されている zip　ファイルを削除する
            var materialPath = Path.Combine(this.Configuration.Deployment.SharedDirectory, ReleaseConstants.MaterialFileNameJproDeploymentService);
            if (FileAccessor.Exists(materialPath))
            {
                FileAccessor.Delete(materialPath);
            }

            this.Logger.InformationLog("JPROリリース機能の自己インストーラーを起動します。");
            await this.InvokeInstaller(workDirPath);
            this.Logger.InformationLog("JPROリリース機能の自己インストーラーを完了します。");
        }

        /// <summary>
        /// リソース解放機能処理.
        /// </summary>
        public void Dispose()
        {
            this.ReleaseFileWatcher();

            // 自己インストールタスクとの排他制御シグナルを獲得している場合リリースする
            this.InstallSignal?.Release();
        }

        /// <summary>
        /// ファイル変更監視開始処理.
        /// インストールディレクトリを参照してインストールzipファイルが配信されるまで通知を行う.
        /// </summary>
        /// <returns>読み取りされたディレクトリパス情報.</returns>
        private async Task<string> ObserveFileUpdateQueue()
        {
            // ファイル監視
            this.FileWatcher = new FileWatcher(
                this.Configuration.Etc.FileDebounceInterval,
                this.SupplyLogger<FileWatcher>());

            this.FileWatcher.FileChangeAction = this.HandleFileUpdateQueue;

            // フォルダ待機開始 (メインループはファイル側で待機する)
            Task<string> workDir = this.FileWatcher.ObserveDirectory(
                this.Configuration.Deployment.SharedDirectory,
                this.CancelToken);

            // 初回イベントを強制発行させる
            this.FileWatcher?.ReigsterEventQueue(this.Configuration.Deployment.SharedDirectory);

            return await workDir;
        }

        /// <summary>
        /// 配信ディレクトリが更新されたときのイベント.
        /// </summary>
        /// <param name="arg">ファイル変更イベント.</param>
        /// <returns>このファイルで待機を終了して良ければ参照するワークファイルパス. 読み取りを継続する場合 null.</returns>
        private string HandleFileUpdateQueue(FileChangeEventArg arg)
        {
            // ファイルが存在するか確認
            var materialPath = Path.Combine(this.Configuration.Deployment.SharedDirectory, ReleaseConstants.MaterialFileNameJproDeploymentService);
            if (!this.TestFile(materialPath))
            {
                this.Logger.InformationLog($"リリース用資材 ({materialPath}) が配置されていません。");
                return null;
            }

            var tempDirectory = Path.Combine(
                this.Configuration.Deployment.TempDirectory,
                Guid.NewGuid().ToString().Replace("-", string.Empty));

            // 処理終了後にテンポラリフォルダパスを削除するかのフラグ
            // 処理に成功しない限り true にならない
            var preserveTempFolder = false;
            try
            {
                // テンポラリフォルダにzip資材を展開
                DirectoryAccessor.CreateDirectory(tempDirectory);

                var tempFilePath = Path.Combine(tempDirectory, ReleaseConstants.MaterialFileNameJproDeploymentService); // テンポラリへの zip のパス
                var workDirPath = Path.Combine(tempDirectory, Guid.NewGuid().ToString().Replace("-", string.Empty)); // zip の展開先

                // テンポラリフォルダに zip を移動する
                File.Copy(materialPath, tempFilePath);

                // zip を展開する
                ArchiveUtility.ExpandArchive(tempFilePath, workDirPath);

                // 実行ファイルのチェック
                var filePath = Path.Combine(workDirPath, "JproDeploymentService.exe");

                // ファイルが存在しない場合
                if (!File.Exists(filePath))
                {
                    this.Logger.WarningLog("JPROリリース機能の実行ファイルが存在しません。");
                    return null;
                }

                // バージョン確認
                var currentVersion = AppVersion.From(FileVersionInfo.GetVersionInfo(this.GetType().Assembly.Location).FileVersion, ".");
                var nextVersion = AppVersion.From(FileVersionInfo.GetVersionInfo(filePath).FileVersion, ".");

                if (currentVersion >= nextVersion)
                {
                    this.Logger.WarningLog($"リリースされているバージョンは過去のリリースバージョンです。現在のバージョン： {currentVersion} リリースされたバージョン： {nextVersion}");
                    return null;
                }

                // リリース対象バージョンなら応答
                preserveTempFolder = true;
                return workDirPath;
            }
            finally
            {
                if (!this.Configuration.Etc.PreserveMaterials &&
                    !preserveTempFolder &&
                    DirectoryAccessor.Exists(tempDirectory))
                {
                    // テンポラリフォルダを削除する
                    DirectoryAccessor.DeleteDirectory(tempDirectory);
                }
            }
        }

        /// <summary>
        /// インストーラー起動処理.
        /// 自己インストーラーを実行する.
        /// </summary>
        /// <param name="workDirPath">>新しいバージョンの自己インストーラーが展開されたパス.</param>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        private async Task InvokeInstaller(string workDirPath)
        {
            // 実行ファイルのチェック
            var filePath = Path.Combine(workDirPath, "JproDeploymentService.exe");
            await foreach (var result in ProcessX.StartAsync($"{filePath} -i", workingDirectory: workDirPath))
            {
                this.Logger.DebugLog(result);
            }
        }

        /// <summary>
        /// ファイルアクセスチェック処理.
        /// </summary>
        /// <param name="path">チェック対象のファイル.</param>
        /// <returns>true の場合、ファイルが存在してアクセス可能. false の場合、それ以外.</returns>
        private bool TestFile(string path)
        {
            try
            {
                // ファイルが存在しない場合、エラー
                if (!File.Exists(path))
                {
                    return false;
                }

                // ファイルがロックできるか確認
                using var stream = new FileStream(path, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// ロガー生成処理.
        /// </summary>
        /// <typeparam name="TCategory">ロガーのカテゴリクラス.</typeparam>
        /// <returns>ファイルロガー.</returns>
        private FileLogger<TCategory> SupplyLogger<TCategory>()
        {
            return FileLogger<TCategory>.CreateFileLogger(
                this.LoggerFactory.CreateLogger<TCategory>(),
                this.MessageProvider);
        }

        /// <summary>
        /// ファイル変更監視終了処理.
        /// </summary>
        private void ReleaseFileWatcher()
        {
            this.FileWatcher?.Dispose();
            this.FileWatcher = null;
        }
    }
}
