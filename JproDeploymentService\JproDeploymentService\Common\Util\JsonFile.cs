﻿// <copyright file="JsonFile.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Util
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Text;
    using System.Text.Encodings.Web;
    using System.Text.Json;
    using System.Text.Json.Nodes;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproBackend.JproBackCommon.Common.Utility.FileAccess;

    /// <summary>
    /// 任意のディスクに保存されたJSONファイルを編集するための機能.
    /// </summary>
    internal sealed class JsonFile
    {
        /// <summary>
        /// <see cref="JsonFile"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        public JsonFile()
        {
            this.Json = JsonSerializer.Deserialize<JsonObject>("{}", new JsonSerializerOptions
            {
                Encoder = JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All),
            });
        }

        /// <summary>
        /// <see cref="JsonFile"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="jsonFile">JSONファイルパス.</param>
        public JsonFile(string jsonFile)
        {
            this.ReadFile(jsonFile);
        }

        /// <summary>
        /// 指定されたパスのJSONファイルを読出しする.
        /// </summary>
        /// <param name="path">JSONファイルパス.</param>
        public void ReadFile(string path)
        {
            var textContet = File.ReadAllText(path);
            this.Json = JsonSerializer.Deserialize<JsonObject>(textContet, new JsonSerializerOptions
            {
                Encoder = JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All),
            });
        }

        /// <summary>
        /// 指定されたパスのJSONファイルを書き出しする.
        /// </summary>
        /// <param name="path">JSONファイルパス.</param>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        public async Task SaveFile(string path)
        {
            using var file = new FileStream(path, FileMode.OpenOrCreate, FileAccess.Write);
            await JsonSerializer.SerializeAsync(file, this.Json, new JsonSerializerOptions
            {
                Encoder = JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRanges.All),
                WriteIndented = true,
            });
        }

        /// <summary>
        /// 指定されたキーの値を文字列として読出しする.
        /// </summary>
        /// <typeparam name="TValue">キーの型.</typeparam>
        /// <param name="key">キー.</param>
        /// <returns>読出しされた値.</returns>
        public TValue GetValue<TValue>(string key)
        {
            // キーが存在しない場合 null
            if (this.GetProperty(key, this.Json) is not JsonObject node)
            {
                return default;
            }

            return node[this.ParsePath(key).targeNode].GetValue<TValue>();
        }

        /// <summary>
        /// 指定されたキーの値を文字列として書き出しする.
        /// </summary>
        /// <typeparam name="TValue">値の型.</typeparam>
        /// <param name="key">キー.</param>
        /// <param name="value">値.</param>
        public void SetValue<TValue>(string key, TValue value)
        {
            if (this.GetProperty(key, this.Json) is JsonObject node)
            {
                var nodeName = this.ParsePath(key).targeNode;
                node[nodeName] = JsonValue.Create(value);
            }
        }

        /// <summary>
        /// 指定されたキーのJSONノードを取得する.
        /// </summary>
        /// <param name="key">キー.</param>
        /// <param name="node">検索を行うノード.</param>
        /// <returns>見つかったノード.</returns>
        private JsonObject GetProperty(string key, JsonObject node)
        {
            return this.ParsePath(key).parents
                .Aggregate((JsonNode)node, (elemnet, key) =>
                {
                    if (elemnet is null)
                    {
                        return null;
                    }

                    if (int.TryParse(key, out var index) && key == index.ToString())
                    {
                        if (elemnet[index] is not null)
                        {
                            return elemnet[index];
                        }
                    }
                    else
                    {
                        if (elemnet[key] is not null)
                        {
                            return elemnet[key];
                        }
                    }

                    return null;
                }) as JsonObject;
        }

        /// <summary>
        /// JSON のパス情報を分解する.
        /// </summary>
        /// <param name="key">JSONのパス情報.</param>
        /// <returns>分解された JSON パス (対象ノードまでのパス, 対象ノード).</returns>
        private (string[] parents, string targeNode) ParsePath(string key)
        {
            var reverse = key.Split(".", StringSplitOptions.RemoveEmptyEntries).Reverse();
            return (reverse.Skip(1).Reverse().ToArray(), reverse.First());
        }

        /// <summary>
        /// 内部で管理している JSON オブジェクト.
        /// </summary>
        private JsonObject Json { get; set; }
    }
}
