﻿// <copyright file="InstallTask.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using JproBackend.Common.Utility.SystemDateTime;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;
    using JproDeploymentService.Service.Operation;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// インストールタスククラス.
    /// 1回分のインストール作業のラウンドトリップを制御するための管理クラス.
    /// </summary>
    internal sealed class InstallTask : IDisposable
    {
        /// <summary>
        /// <see cref="InstallTask"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="loggerFactory">ロガーファクトリ.</param>
        /// <param name="environment">環境データ.</param>
        /// <param name="messageProvider">メッセージ情報取得処理.</param>
        public InstallTask(
            ILoggerFactory loggerFactory,
            IHostEnvironment environment,
            IMessageProvider messageProvider)
        {
            this.LoggerFactory = loggerFactory;
            this.Logger = this.SupplyLogger<InstallTask>();

            this.Configuration = InternalActivator.CreateConfiguration(environment.EnvironmentName, false)
                .ToJproReleaseConfiguration();

            this.MessageProvider = messageProvider;
        }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private ILoggerFactory LoggerFactory { get; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<InstallTask> Logger { get; }

        /// <summary>
        /// メッセージ取得クラスを取得する.
        /// </summary>
        private IMessageProvider MessageProvider { get; }

        /// <summary>
        /// データベース監視処理を取得または設定する.
        /// </summary>
        private DbWatcher DbWatcher { get; set; }

        /// <summary>
        /// フォルダ監視処理を取得または設定する.
        /// </summary>
        private FileWatcher FileWatcher { get; set; }

        /// <summary>
        /// フォルダのzipファイル監視処理を取得または設定する.
        /// </summary>
        private FileWatcher ZipWatcher { get; set; }

        /// <summary>
        /// 1回分のインストール作業のラウンドトリップのキャンセルトークン.
        /// DeploymentServiceMain がキャンセル指示を実行する.
        /// </summary>
        private CancellationToken CancelToken { get; set; }

        /// <summary>
        /// 1回分のインストール作業のラウンドトリップが正しく完了したことを表すシグナル.
        /// </summary>
        private SemaphoreSlim DisposingSignal { get; set; }

        /// <summary>
        /// JPROのインストールタスクとJRPOリリース機能の自己インストールタスクを排他制御するためのシグナル.
        /// </summary>
        private SemaphoreSlim InstallSignal { get; set; }

        /// <summary>
        /// インストール処理.
        /// 1回分のインストール作業を実行する.
        /// </summary>
        /// <param name="cancelToken">インストール処理を停止するためのトークン.</param>
        /// <param name="installSignal">インストールタスクの排他制御シグナル.</param>
        /// <returns>インストールされたバージョン.</returns>
        public async Task<AppVersion> Execute(CancellationToken cancelToken, SemaphoreSlim installSignal)
        {
            // installSignal は実際にデータとファイルが準備されてインストール処理が開始してからプロパティでキャプチャする
            this.CancelToken = cancelToken;

            // 初めにセマフォを取得する (このオブジェクトが Dispose されるまで解放されない)
            this.DisposingSignal = new SemaphoreSlim(1);
            this.DisposingSignal.Wait();

            // 継続的ファイル監視を開始（タイミング問題を解決するため）
            this.StartContinuousFileWatching(cancelToken);

            // 呼び出し元からキャンセルされるか
            // バージョン管理テーブルが更新されて新しいバージョンのインストール設定がキューイングされるまで待機する
            List<AppVersionControl> appVersionControl = await this.ObserveDbUpdateQueue();

            // 一旦DBの監視を解放する
            this.ReleaseDbWatcher();

            // キャンセル終了した場合、終了
            if (cancelToken.IsCancellationRequested ||
                appVersionControl is null ||
                !appVersionControl.Any())
            {
                return null;
            }

            // ログ出力
            this.OuputObserveDbResultLog(appVersionControl);

            // ファイル監視開始（既存のファイルもチェック）
            string workDirPath = await this.ObserveFileUpdateQueueWithContinuousWatching(appVersionControl);

            // DB・ファイルの監視を解放する
            this.ReleaseDbWatcher();
            this.ReleaseZipWatcher();
            this.ReleaseFileWatcher();

            // キャンセル終了した場合、終了
            // 展開したディレクトリからバージョン情報を取得できない場合も終了
            if (cancelToken.IsCancellationRequested ||
                workDirPath is null ||
                this.GetReleaseVersionFileVersion(workDirPath) is not AppVersion version)
            {
                return null;
            }

            // インストール準備が完了
            this.Logger.InformationLog(
                $"リリース対象の資材ファイルを検知しました。リリース作業用フォルダは {workDirPath} です。({version})");

            // ここから自己インストール機能へのロックを開始する
            // ループの頻度を下げるために10秒間待機している
            if (!installSignal.Wait(10 * 1000, cancelToken))
            {
                this.Logger.InformationLog("JPROリリース機能の自己インストール処理が実行中のためJPROのインストール処理を中断します。");
                return null;
            }

            // ロックできたら、プロパティにキャプチャして Dispose 時にリリースする
            this.InstallSignal = installSignal;

            // 空きポートの取得
            int? port = await this.GetUnsedPort();

            // キャンセル終了した場合、終了
            if (cancelToken.IsCancellationRequested || port is null)
            {
                return null;
            }

            // インストール準備が完了
            this.Logger.InformationLog($"利用ポートとして {port} を取得しました。({version})");

            // 設定ファイルの更新を行う
            var fileOperation = this.CreateFileOperation();

            // 配信 zip を削除する
            fileOperation.RemoveMatrialZip();

            // バックエンドの設定ファイル(appsettings.Production.json) に店舗固有の情報を記載する
            await fileOperation.UpdateBackendAppSetting(workDirPath, port.Value, version);
            this.Logger.InformationLog($"バックエンドの設定ファイルを更新しました。({version})");

            // フロントエンドの Base Path 設定を更新する
            fileOperation.UpdateFrontendBasePath(workDirPath, version);
            this.Logger.InformationLog($"フロントエンドの既定パスを更新しました。({version})");

            // Nngix が参照する設定ファイルにポート設定を追加する
            fileOperation.CreateNginxSetting(version, port.Value, this.CreateEnvironmentUtility());
            this.Logger.InformationLog($"リリース用のNginxファイル設定を生成しました。({version})");

            // 各ファイルを本番リリースフォルダに配置する
            fileOperation.MoveToInstallPath(workDirPath, version);
            this.Logger.InformationLog($"バックエンド・フロントエンドのインストールフォルダに展開しました。({version})");

            // サーバーを起動する
            var serviceOperation = this.CreateWindowsServiceOperation();

            // Start JPRO from Command line
            var commandLineOperation = this.CreateCommandLineOperation();
            await commandLineOperation.StartJproBackend(version, port.Value);
            this.Logger.InformationLog($"バックエンドサービスを起動しました。({version})");

            // フロントエンド設定を更新する
            await serviceOperation.ReloadNginxConfig(version);
            this.Logger.InformationLog($"Nginx の設定をリロードしました。({version})");

            // テンポラリフォルダを削除する
            fileOperation.DeleteWorkDirPath(workDirPath);

            return version;
        }

        /// <summary>
        /// リソース解放機能処理.
        /// リソースを破棄する.
        /// </summary>
        public void Dispose()
        {
            this.ReleaseDbWatcher();
            this.ReleaseFileWatcher();
            this.ReleaseZipWatcher();

            // 自己インストールタスクとの排他制御シグナルを獲得している場合リリースする
            this.InstallSignal?.Release();

            // セマフォをリリースして処理を完了しても良いことを表すシグナルにする
            this.DisposingSignal?.Release();
        }

        /// <summary>
        /// 停止待機処理.
        /// メイン処理(Execute)が完了するまで待機する.
        /// Dispose が呼ばれたら待機が解除される.
        /// </summary>
        /// <param name="timeout">タイムアウト(秒).</param>
        /// <returns>タイムアウトせず終了した場合 true. タイムアウトした場合 false.</returns>
        public bool WaitFinalize(int timeout)
        {
            if (this.DisposingSignal is not SemaphoreSlim signal)
            {
                return true;
            }

            // Dispose が呼ばれるまで待機する
            if (signal.Wait(timeout * 1000))
            {
                // 上で取得したセマフォを解放
                signal.Release();
                return true;
            }
            else
            {
                // Dispose が実行されるまでにタイムアウトした場合
                return false;
            }
        }

        /// <summary>
        /// DB変更監視開始処理.
        /// アプリケーションバージョン制御テーブルを参照して
        /// 現在より新しいバージョンのファイルが配信されるまで通知を行う.
        /// </summary>
        /// <returns>読み取りされたバージョン管理テーブル情報.</returns>
        private Task<List<AppVersionControl>> ObserveDbUpdateQueue()
        {
            this.DbWatcher = new DbWatcher(
                this.Configuration.Etc.DbDebounceInterval,
                this.SupplyLogger<DbWatcher>(),
                this.SupplyLogger<DbTableWatcher>(),
                this.Configuration);

            // DB更新イベントを割り当てる
            this.DbWatcher.DbChangeAction = this.HandleDbUpdateQueue;

            // 待機開始
            return this.DbWatcher.ObserveTables(this.CancelToken);
        }

        /// <summary>
        /// DB変更時イベントハンドラー処理.
        /// DBが更新されたときのイベント.
        /// 現在のDBバージョンより新しい更新が登録された処理を完了する.
        /// </summary>
        /// <param name="args">更新されたDB情報.</param>
        /// <returns>このDB情報で待機を終了して良ければ対象のバージョン管理テーブル情報. 読み取りを継続する場合 null.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S1168:Empty arrays and collections should be returned instead of null", Justification = "<保留中>")]
        private List<AppVersionControl> HandleDbUpdateQueue(DbChangeEventArg args)
        {
            this.Logger.InformationLog($"バージョン管理テーブルが更新されました。 識別コード: {args.Id} イベント発生時間: {args.EventTime}");

            // レコードが存在しない場合、継続
            if (!args.Parameters.Any())
            {
                this.Logger.InformationLog($"バージョン管理テーブルにデータが存在しません。");
                return null;
            }

            // インストール対象のレコードが存在することを確認
            if (this.GetDbTargetVersion(args) is not List<AppVersionControl> targetVersion)
            {
                this.Logger.InformationLog("バージョン管理テーブルにリリース対象のデータが存在しません。");
                return null;
            }

            return targetVersion;
        }

        /// <summary>
        /// リリース対象バージョン確認処理.
        /// バージョン管理テーブルより取得したバージョンテーブルから現在有効なバージョンを取得する.
        /// </summary>
        /// <param name="args">DBの変更検知で取得したバージョン管理テーブルのデータセット.</param>
        /// <returns>現在、有効なバージョン.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S1168:Empty arrays and collections should be returned instead of null", Justification = "<保留中>")]
        private List<AppVersionControl> GetDbTargetVersion(DbChangeEventArg args)
        {
            // DB上のバージョン情報一覧
            var versions = args.Parameters.Select(version =>
                    (Version: AppVersion.From(version), AppVersion: version))
                .ToList();

            // リリース対象のデータが存在するか確認する
            if (!versions.Any())
            {
                return null;
            }

            var current = SystemDateTime.Now;
            AppVersion currentVersion = this.CreateEnvironmentUtility().GetJproServiceVersion();

            // 現在インストール済みのバージョン以上のバージョンで
            // 終了日が現在時刻の範囲内のデータはすべてインストール対象にする
            var targetVersions = versions
                .Where(version => currentVersion is null || currentVersion < version.Version)
                .Where(version => current <= version.AppVersion.LastDate);

            // 1件でもデータがあればリストとして返却する
            if (targetVersions.Any())
            {
                return targetVersions.Select(version => version.AppVersion).ToList();
            }

            return null;
        }

        /// <summary>
        /// ファイル変更監視開始処理.
        /// インストールディレクトリを参照してインストールzipファイルが配信されるまで通知を行う.
        /// </summary>
        /// <param name="targetVersions">インストール対象のバージョン.</param>
        /// <returns>読み取りされたディレクトリパス情報.</returns>
        private async Task<string> ObserveFileUpdateQueue(List<AppVersionControl> targetVersions)
        {
            // フォルダ監視
            this.DbWatcher = new DbWatcher(
                this.Configuration.Etc.DbDebounceInterval,
                this.SupplyLogger<DbWatcher>(),
                this.SupplyLogger<DbTableWatcher>(),
                this.Configuration);

            // DeploymentServiceMain が提供するメインループのキャンセルトークンと
            // DB監視クラス向けのキャンセルトークンを合成したトークンを生成する
            using var internalTokenSource = CancellationTokenSource.CreateLinkedTokenSource(this.CancelToken);

            // DB更新イベントを割り当てる
            // マニュアルでキャンセルするためソースを監視イベントに引き渡す
            this.DbWatcher.DbChangeAction =
                this.CreateHandleDbUpdateQueueForFileWatcher(targetVersions, internalTokenSource);

            // DB待機開始
            // 監視中にデータベースが変更された場合に
            _ = this.DbWatcher.ObserveTables(internalTokenSource.Token);

            // リリース適用期間になったバージョンの一覧
            ConcurrentBag<AppVersionControl> waitingTargetVersion = new ConcurrentBag<AppVersionControl>();

            // フォルダ内のzipファイル監視
            this.ZipWatcher = new FileWatcher(
                this.Configuration.Etc.FileDebounceInterval,
                this.SupplyLogger<FileWatcher>());

            this.ZipWatcher.FileChangeAction = this.CreateHandleZipFileUpdateQueue();

            // フォルダ監視開始 (メインループはファイル側で待機する)
            var observeZipTask = this.ZipWatcher.ObserveDirectory(
                this.Configuration.Deployment.SharedDirectory,
                internalTokenSource.Token);

            // 時間経過でファイル監視処理を強制実行するイベントを開始する
            _ = this.StartWaitingTargetVersionEvent(targetVersions, waitingTargetVersion, internalTokenSource);

            // ファイル展開されるまで待機開始
            var zipFilePath = await observeZipTask;

            // フォルダ監視
            this.FileWatcher = new FileWatcher(
                this.Configuration.Etc.FileDebounceInterval,
                this.SupplyLogger<FileWatcher>());

            // ファイル変更イベントを割り当てる
            this.FileWatcher.FileChangeAction = this.CreateHandleFileUpdateQueue(waitingTargetVersion);

            // フォルダ監視開始 (メインループはファイル側で待機する)
            var observeTask = this.FileWatcher.ObserveDirectory(
                this.Configuration.Deployment.SharedDirectory,
                internalTokenSource.Token);

            // 時間経過でファイル監視処理を強制実行するイベントを開始する
            _ = this.StartWaitingTargetVersionEvent(targetVersions, waitingTargetVersion, internalTokenSource);

            // ファイル展開されるまで待機開始
            var workDirPath = await observeTask;

            // DB監視処理側でキャンセルされていない場合、そちらをキャンセルする
            if (!internalTokenSource.IsCancellationRequested)
            {
                internalTokenSource.Cancel();
            }

            return workDirPath;
        }

        /// <summary>
        /// ファイル変更時イベントハンドラー生成処理.
        /// 配信ディレクトリが更新されたときのイベントを生成する.
        /// 現在のDBバージョンに相当するファイルが登録された処理を完了する.
        /// </summary>
        /// <param name="targetVersion">ファイルが監視しているバージョン.</param>
        /// <returns>
        /// ファイル監視イベント.
        /// イベントの戻り値は「このファイルで待機を終了して良ければ参照するワークファイルパス. 読み取りを継続する場合 null」.</returns>
        private Func<FileChangeEventArg, string> CreateHandleFileUpdateQueue(IEnumerable<AppVersionControl> targetVersion)
        {
            // ファイルディレクトリが更新された場合
            return args =>
            {
                // targetVersion は別スレッドで都度更新されるので、イベント毎に再度評価する
                var versions = targetVersion.Select(ver => AppVersion.From(ver)).ToList();

                if (!targetVersion.Any())
                {
                    this.Logger.InformationLog("現在適用可能な期間のバージョンが存在しません。");
                    return null;
                }

                FileOperation fileOperation = this.CreateFileOperation();

                // 監視されたファイルがリリース対象のリリースフォルダの場合（リトライ機能付き）
                // 非同期メソッドを同期的に実行
                try
                {
                    var task = fileOperation.CopyMaterialZipWithRetry(versions);
                    var workFile = task.GetAwaiter().GetResult();
                    if (workFile is not null)
                    {
                        this.Logger.InformationLog("リリース対象ファイルを検知しました。");
                        return workFile;
                    }
                }
                catch (Exception ex)
                {
                    this.Logger.ErrorLog(ex, "ファイル監視処理中にエラーが発生しました。");
                }

                // 処理を継続する場合
                return null;
            };
        }

        private Func<FileChangeEventArg, string> CreateHandleZipFileUpdateQueue()
        {
            return args =>
            {
                this.Logger.InformationLog("マージのプロセスは開始しました。");
                var metadataFilePath = Path.Combine(this.Configuration.Deployment.SharedDirectory, ReleaseConstants.MaterialFileNameJproReleaseMetadata);
                var inputFolderPath = this.Configuration.Deployment.SharedDirectory;
                var outputFilePath = Path.Combine(this.Configuration.Deployment.SharedDirectory, ReleaseConstants.MaterialFileNameJproRelease);
                ZipMerge zipMerge = new ZipMerge(metadataFilePath, inputFolderPath, outputFilePath, this.SupplyLogger<ZipMerge>());
                if (zipMerge.MergeFiles() is string output)
                {
                    zipMerge.RemoveFiles(inputFolderPath, ReleaseConstants.MaterialFileNameJproRelease);
                    return output;
                }

                return null;
            };
        }

        /// <summary>
        /// ファイル監視時のDB変更時イベントハンドラー生成処理.
        /// ファイル監視中にDBのバージョン管理テーブルが更新された場合に、監視処理を解放するためのイベントを生成する.
        /// </summary>
        /// <param name="targetVersion">ファイルが監視しているバージョン.</param>
        /// <param name="internalTokenSource">監視を内部から解除するためのキャンセルトークンソース.</param>
        /// <returns>ファイル監視中にDBのバージョン管理テーブルが更新された場合に、監視処理を解放するためのイベント.</returns>
        private Func<DbChangeEventArg, List<AppVersionControl>> CreateHandleDbUpdateQueueForFileWatcher(
            List<AppVersionControl> targetVersion,
            CancellationTokenSource internalTokenSource)
        {
            return args =>
            {
                // ファイル監視中にDBが更新された場合
                var newTargetVersion = this.GetDbTargetVersion(args);

                // 監視対象のソースが削除された場合
                if (newTargetVersion is null)
                {
                    // ファイルとDBの監視処理を停止
                    this.Logger.WarningLog("バージョン管理テーブルから待機中のリリース資材に対応するエントリが削除されました。");
                    internalTokenSource.Cancel(); // DB監視側からファイル監視側のタスクを停止する
                    return newTargetVersion;
                }

                // 待機対象の内容が変更されていないことを確認する
                bool targetNotChanged = targetVersion.Count == newTargetVersion.Count;
                targetNotChanged = targetNotChanged && targetVersion.All(version =>
                {
                    // 更新後のデータにも対象のバージョンが存在している
                    if (newTargetVersion.FirstOrDefault(newVersion => AppVersion.From(newVersion) == AppVersion.From(version))
                        is not AppVersionControl newVersion)
                    {
                        return false;
                    }

                    // 開始日・終了日が変更されていない
                    return version.StartDate == newVersion.StartDate && version.LastDate == newVersion.LastDate;
                });

                // 監視中のデータに変更があった場合
                if (!targetNotChanged)
                {
                    // ファイルとDBの監視処理を停止
                    this.Logger.WarningLog("バージョン管理テーブルにより新しいリリース可能なエントリが更新されました。");
                    internalTokenSource.Cancel(); // DB監視側からファイル監視側のタスクを停止する
                    return newTargetVersion;
                }

                // DB監視を継続する
                return null;
            };
        }

        /// <summary>
        /// ファイル更新イベント強制発生処理.
        /// 時刻経過にもとづいてリリース可能なバージョンを更新しながら、ファイル監視イベントを強制実行する.
        /// </summary>
        /// <param name="targetVersion">DB監視処理で取得したすべてのリリース対象のバージョン.</param>
        /// <param name="waitingTargetVersion">ファイル監視している現在リリース可能なバージョン.</param>
        /// <param name="internalTokenSource">監視を内部から解除するためのキャンセルトークンソース.</param>
        /// <returns>このメソッドは戻り値のない非同期処理.</returns>
        private async Task StartWaitingTargetVersionEvent(
            List<AppVersionControl> targetVersion,
            ConcurrentBag<AppVersionControl> waitingTargetVersion,
            CancellationTokenSource internalTokenSource)
        {
            // すべてのイベント処理の発生時刻を取得
            var eventDateList = targetVersion.SelectMany(version => new[] { version.StartDate, version.LastDate })
                .Distinct()
                .OrderBy(date => date)
                .ToList();

            var current = SystemDateTime.Now;

            // ファイル監視処理の開始時点で時間が経過済みの場合
            if (eventDateList.Any(date => date <= current))
            {
                // 現在時刻以前のデータは削除
                eventDateList = eventDateList.Where(date => date <= current).ToList();

                // ファイル監視対象のバージョンを更新してファイル監視イベントを実行する
                UpdateWaitingTargetVersion();
            }

            // 現在時刻以降の処理
            foreach (DateTime date in eventDateList)
            {
                // リリース開始時間まで待機
                current = SystemDateTime.Now;

                // 何らかの理由でシステム時刻が現在時間を超過している場合
                if (date <= current)
                {
                    // ファイル監視対象のバージョン番号を更新して、次の更新時刻に遷移する
                    UpdateWaitingTargetVersion();
                    continue;
                }

                // リリース開始・終了時刻まで待機する
                var waitSpan = date - current;

                // 現実的な待機時間(ほとんどの場合 9999/12/31) を超過した場合、待機せずに処理を終了する
                if (waitSpan.TotalMilliseconds >= int.MaxValue)
                {
                    // ファイル監視対象のバージョン番号を更新する
                    break;
                }

                // 次のリリースまで待機する
                OutputDelayLog(date, waitSpan); // 待機ログを記録
                await Task.Delay((int)waitSpan.TotalMilliseconds, internalTokenSource.Token);

                // 待機中にキャンセルされた場合、ファイル監視処理を中断する
                if (this.CancelToken.IsCancellationRequested || internalTokenSource.IsCancellationRequested)
                {
                    // DB監視処理側でキャンセルされていない場合、そちらもキャンセルする
                    if (!internalTokenSource.IsCancellationRequested)
                    {
                        internalTokenSource.Cancel();
                    }

                    return;
                }

                // ファイル監視対象のバージョン番号を更新する
                UpdateWaitingTargetVersion();
            }

            // ファイル監視対象のバージョン番号を更新する処理
            void UpdateWaitingTargetVersion()
            {
                // リリース開始時間まで待機
                var current = SystemDateTime.Now;

                // 監視対象のバージョンに追加に追加
                waitingTargetVersion.Clear();
                var currentTarget = targetVersion.Where(version => version.StartDate <= current && current <= version.LastDate);
                currentTarget.ToList().ForEach(version => waitingTargetVersion.Add(version));

                this.Logger.InformationLog("現在ファイル監視中のリリースバージョンは以下のとおりです。");
                OutputVersionLog(currentTarget);

                this.FileWatcher?.ReigsterEventQueue(this.Configuration.Deployment.SharedDirectory);
            }

            // 待機の処理のためにログ出力する
            void OutputDelayLog(DateTime date, TimeSpan span)
            {
                var availableVersion = targetVersion.Where(version => version.StartDate == date || version.LastDate == date).ToList();
                this.Logger.InformationLog($"以下のリリースバージョンの適用開始・終了のため、{(int)span.TotalSeconds}秒間待機を行います");
                OutputVersionLog(availableVersion);
            }

            // リリース対象の開始・終了バージョンを出力する
            void OutputVersionLog(IEnumerable<AppVersionControl> versions)
            {
                versions.ToList().ForEach(version =>
                    this.Logger.InformationLog($"バージョン v{AppVersion.From(version)} 開始日 {version.StartDate.ToString("yyyy/MM/dd HH:mm:ss")} 終了日 {version.LastDate.ToString("yyyy/MM/dd HH:mm:ss")}"));
            }
        }

        /// <summary>
        /// 未使用ポート取得処理.
        /// 空きポートを取得する.
        /// </summary>
        /// <returns>ポート.</returns>
        private async Task<int?> GetUnsedPort()
        {
            for (var index = 0; index < this.Configuration.Etc.UnusedPortRetryCount; index++)
            {
                if (index > 0)
                {
                    this.Logger.InformationLog($"空きポートの取得に失敗しました。{index}回目の待機を行います。");
                    await Task.Delay(this.Configuration.Etc.UnusedPortRetryInterval * 1000, this.CancelToken);
                }

                var port = this.CreateEnvironmentUtility().GetUnusedPort();
                if (port.HasValue)
                {
                    return port.Value;
                }
            }

            return null;
        }

        /// <summary>
        /// ファイル処理操作クラス生成処理.
        /// </summary>
        /// <returns>ファイル操作機能.</returns>
        private FileOperation CreateFileOperation()
        {
            return new FileOperation(
                this.SupplyLogger<FileOperation>(),
                this.Configuration);
        }

        /// <summary>
        /// Windowsサービス操作クラス生成処理.
        /// </summary>
        /// <returns>Windows サービス操作機能.</returns>
        private WindowsServiceOperation CreateWindowsServiceOperation()
        {
            return new WindowsServiceOperation(
                this.SupplyLogger<WindowsServiceOperation>(),
                this.Configuration);
        }

        /// <summary>
        /// コマンドライン操作クラス生成処理.
        /// </summary>
        /// <returns>Windows サービス操作機能.</returns>
        private CommandLineOperation CreateCommandLineOperation()
        {
            return new CommandLineOperation(
                this.SupplyLogger<CommandLineOperation>(),
                this.Configuration);
        }

        /// <summary>
        /// 環境情報ユーティリティクラス生成処理.
        /// </summary>
        /// <returns>ファイル操作機能.</returns>
        private EnvironmentUtility CreateEnvironmentUtility()
        {
            // 現在のインストールバージョンを確認
            return new EnvironmentUtility(
                this.Configuration,
                this.SupplyLogger<EnvironmentUtility>());
        }

        /// <summary>
        /// ロガー生成処理.
        /// </summary>
        /// <typeparam name="TCategory">ロガーのカテゴリクラス.</typeparam>
        /// <returns>ファイルロガー.</returns>
        private FileLogger<TCategory> SupplyLogger<TCategory>()
        {
            return FileLogger<TCategory>.CreateFileLogger(
                this.LoggerFactory.CreateLogger<TCategory>(),
                this.MessageProvider);
        }

        /// <summary>
        /// DB変更監視終了処理.
        /// </summary>
        private void ReleaseDbWatcher()
        {
            this.DbWatcher?.Dispose();
            this.DbWatcher = null;
        }

        /// <summary>
        /// ファイル変更監視終了処理.
        /// </summary>
        private void ReleaseFileWatcher()
        {
            this.FileWatcher?.Dispose();
            this.FileWatcher = null;
        }

        private void ReleaseZipWatcher()
        {
            this.ZipWatcher?.Dispose();
            this.ZipWatcher = null;
        }
        /// <summary>
        /// DB監視処理で取得したバージョン管理テーブル情報をログ出力する.
        /// </summary>
        /// <param name="appVersionControl">インストール対象のバージョン.</param>
        private void OuputObserveDbResultLog(IEnumerable<AppVersionControl> appVersionControl)
        {
            this.Logger.InformationLog($"バージョン管理テーブルにリリース可能なバージョンを{appVersionControl.Count()}件検知しました。");

            foreach (var appVersion in appVersionControl)
            {
                var version = AppVersion.From(appVersion);
                this.Logger.InformationLog(
                    $"バージョン {version} " +
                    $"リリース開始日 {appVersion.StartDate.ToString("yyyy/MM/dd HH:mm:ss")}");
            }
        }

        /// <summary>
        /// バージョン定義ファイルのリリースバージョン取得処理.
        /// </summary>
        /// <param name="workDirPath">リリース資材の一時展開パス.</param>
        /// <returns>バージョン定義ファイル上のバージョン定義.</returns>
        private AppVersion GetReleaseVersionFileVersion(string workDirPath)
        {
            var fileOpearaion = this.CreateFileOperation();
            return fileOpearaion.GetReleaseVersionFileVersion(workDirPath);
        }

        /// <summary>
        /// 継続的ファイル監視開始処理.
        /// サービス開始時から継続的にファイルを監視し、タイミング問題を解決する.
        /// </summary>
        /// <param name="cancelToken">キャンセルトークン.</param>
        private void StartContinuousFileWatching(CancellationToken cancelToken)
        {
            // 継続的ファイル監視を開始
            this.FileWatcher = new FileWatcher(
                this.Configuration.Etc.FileDebounceInterval,
                this.SupplyLogger<FileWatcher>());

            // ファイル変更イベントを割り当てる（DB状態をチェックする）
            this.FileWatcher.FileChangeAction = this.CreateContinuousFileHandler();

            // フォルダ監視開始（バックグラウンドで継続実行）
            _ = Task.Run(async () =>
            {
                try
                {
                    await this.FileWatcher.ObserveDirectory(
                        this.Configuration.Deployment.SharedDirectory,
                        cancelToken);
                }
                catch (Exception ex)
                {
                    this.Logger.ErrorLog(ex, "継続的ファイル監視中にエラーが発生しました。");
                }
            });

            this.Logger.InformationLog("継続的ファイル監視を開始しました。");
        }

        /// <summary>
        /// 継続的ファイル監視用のハンドラー作成処理.
        /// ファイルが検出された時にDB状態をチェックして処理を決定する.
        /// </summary>
        /// <returns>ファイル変更イベントハンドラー.</returns>
        private Func<FileChangeEventArg, string> CreateContinuousFileHandler()
        {
            return args =>
            {
                try
                {
                    // 各ファイル変更パラメータをチェック
                    foreach (var parameter in args.Parameters)
                    {
                        if (parameter.HasError)
                        {
                            continue; // エラーがある場合はスキップ
                        }

                        // JproRelease.zipファイルかチェック
                        if (parameter.Path?.EndsWith("JproRelease.zip", StringComparison.OrdinalIgnoreCase) == true)
                        {
                            this.Logger.InformationLog($"継続的ファイル監視でJproRelease.zipを検出しました: {parameter.Path}");

                            // 現在のDB状態をチェック
                            // TODO: ここでDB状態をチェックして、対応するバージョンがあるかを確認
                            // 今は単純にファイルの存在をログ出力するのみ

                            break; // JproRelease.zipを見つけたらループを抜ける
                        }
                    }

                    return null; // 継続的監視では処理を完了させない
                }
                catch (Exception ex)
                {
                    this.Logger.ErrorLog(ex, "継続的ファイル監視ハンドラーでエラーが発生しました。");
                    return null;
                }
            };
        }

        /// <summary>
        /// 継続的ファイル監視と連携したファイル監視処理.
        /// 既存のファイルもチェックして、DB状態と照合する.
        /// </summary>
        /// <param name="targetVersions">インストール対象のバージョン.</param>
        /// <returns>読み取りされたディレクトリパス情報.</returns>
        private async Task<string> ObserveFileUpdateQueueWithContinuousWatching(List<AppVersionControl> targetVersions)
        {
            this.Logger.InformationLog("継続的ファイル監視と連携したファイル処理を開始します。");

            // 既存のファイルをまずチェック
            var fileOperation = this.CreateFileOperation();
            var versions = targetVersions.Select(AppVersion.From).ToList();

            // 既にファイルが存在するかチェック（リトライ機能付き）
            if (await fileOperation.CopyMaterialZipWithRetry(versions) is string existingWorkFile)
            {
                this.Logger.InformationLog("既存のJproRelease.zipファイルを検出し、処理を開始します。");
                return existingWorkFile;
            }

            // 既存ファイルがない場合は、従来の監視方式にフォールバック
            return await this.ObserveFileUpdateQueue(targetVersions);
        }
    }
}
