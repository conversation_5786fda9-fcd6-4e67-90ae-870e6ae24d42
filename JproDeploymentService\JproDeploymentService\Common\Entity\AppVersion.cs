﻿// <copyright file="AppVersion.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Entity
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;

    /// <summary>
    /// アプリバージョンを管理します.
    /// </summary>
    internal class AppVersion : IComparable<AppVersion>
    {
        /// <summary>
        /// メジャーバージョン番号を取得または設定する.
        /// </summary>
        public int MajorNum { get; set; }

        /// <summary>
        /// マイナーバージョン番号を取得または設定する.
        /// </summary>
        public int MinorNum { get; set; }

        /// <summary>
        /// ビルドバージョン番号を取得または設定する.
        /// </summary>
        public int BuildNum { get; set; }

        /// <summary>
        /// リビジョンバージョン番号を取得または設定する.
        /// </summary>
        public int RevisionNum { get; set; }

        /// <summary>
        /// バージョン管理テーブルからバージョンオブジェクトを生成する.
        /// </summary>
        /// <param name="appVersion">バージョン管理テーブル.</param>
        /// <returns>バージョン情報.</returns>
        public static AppVersion From(AppVersionControl appVersion)
        {
            return new AppVersion
            {
                MajorNum = appVersion.MajorNum,
                MinorNum = appVersion.MinorNum,
                BuildNum = appVersion.BuildNum,
                RevisionNum = appVersion.RevisionNum,
            };
        }

        /// <summary>
        /// バージョンを表す情報を含む文字列からバージョンオブジェクトを生成する.
        ///
        /// 設計時のパスやサービス名に従い実装しており汎用的な設計になっていません.
        /// 例えば prefix に () など正規表現として有効な表現が含まれる場合、正しく動作しません.
        /// 設計に変更がある場合、この関数が正しく動作するかテストを行ってください.
        /// </summary>
        /// <param name="versionName">
        /// バージョンを表す文字列.
        /// {prefix}{MajorNum}{separator}{MinorNum}{separator}{BuildNum}{separator}{RevisionNum} の形式.
        /// </param>
        /// <param name="separator">区切り文字列.</param>
        /// <param name="prefix">
        /// バージョンの接頭辞、例えば v など.<br/>
        /// () など正規表現として有効な表現が含まれる場合、正しく動作しません. 設計に変更がある場合、この関数が正しく動作するかテストを行ってください.
        /// </param>
        /// <returns>バージョン情報.</returns>
        public static AppVersion From(string versionName, string separator, string prefix = null)
        {
            var regexPrefix = string.IsNullOrEmpty(prefix) ? string.Empty : $"[{prefix}]";
            var regex = @$"{regexPrefix}(\d+){separator}(\d+){separator}(\d+){separator}(\d+)$";

            var match = Regex.Match(versionName, regex);
            if (match.Success)
            {
                try
                {
                    return new AppVersion
                    {
                        MajorNum = int.Parse(match.Groups[1].Value),
                        MinorNum = int.Parse(match.Groups[2].Value),
                        BuildNum = int.Parse(match.Groups[3].Value),
                        RevisionNum = int.Parse(match.Groups[4].Value),
                    };
                }
                catch
                {
                    // noop
                }
            }

            return null;
        }

        /// <summary>
        /// 4つの整数からバージョンオブジェクトを生成する.
        /// 下記で定義した演算子関連テスト用の実装です.本番では使用されません.
        /// </summary>
        /// <param name="majroNum">メジャーバージョン番号.</param>
        /// <param name="minorNum">マイナーバージョン番号.</param>
        /// <param name="buildNum">ビルドバージョン番号.</param>
        /// <param name="revisionNum">リビジョンバージョン番号.</param>
        /// <returns>バージョン情報.</returns>
        public static AppVersion From(int majroNum, int minorNum, int buildNum, int revisionNum)
        {
            return new AppVersion
            {
                MajorNum = majroNum,
                MinorNum = minorNum,
                BuildNum = buildNum,
                RevisionNum = revisionNum,
            };
        }

        /// <summary>
        /// サンプル実装.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Bug", "S1764:Identical expressions should not be used on both sides of a binary operator", Justification = "<保留中>")]
        public static void Sample()
        {
            // ToString
            IsTrue(From(1, 2, 3, 4).ToString() == "1.2.3.4");

            // CompareTo
            IsTrue(From(1, 2, 3, 4).CompareTo(From(1, 2, 3, 4)) == 0);

            IsTrue(From(1, 2, 3, 4).CompareTo(From(1, 2, 3, 5)) < 0);
            IsTrue(From(1, 2, 3, 4).CompareTo(From(1, 2, 4, 4)) < 0);
            IsTrue(From(1, 2, 3, 4).CompareTo(From(1, 3, 3, 4)) < 0);
            IsTrue(From(1, 2, 3, 4).CompareTo(From(2, 2, 3, 4)) < 0);

            IsTrue(From(1, 2, 3, 5).CompareTo(From(1, 2, 3, 4)) > 0);
            IsTrue(From(1, 2, 4, 4).CompareTo(From(1, 2, 3, 4)) > 0);
            IsTrue(From(1, 3, 3, 4).CompareTo(From(1, 2, 3, 4)) > 0);
            IsTrue(From(2, 2, 3, 4).CompareTo(From(1, 2, 3, 4)) > 0);

            IsTrue(From(2, 2, 3, 4).CompareTo(null) > 0);

            IsTrue(new[]
            {
                From(1, 2, 3, 4),
                From(1, 2, 3, 5),
                From(1, 2, 4, 4),
                From(1, 3, 3, 4),
                From(2, 2, 3, 4),
            }.OrderBy(x => x).First() == From(1, 2, 3, 4));

            IsTrue(new[]
            {
                From(1, 2, 3, 4),
                From(1, 2, 3, 5),
                From(1, 2, 4, 4),
                From(1, 3, 3, 4),
                From(2, 2, 3, 4),
            }.OrderByDescending(x => x).First() == From(2, 2, 3, 4));

            // Equels
            IsTrue(From(1, 2, 3, 4).Equals(From(1, 2, 3, 4)));
            IsFalse(From(1, 2, 3, 5).Equals(From(1, 2, 3, 4)));
            IsFalse(From(1, 2, 3, 5).Equals(null));
            IsFalse(From(1, 2, 3, 5).Equals(1234));

            // GetHashCode
            IsTrue(From(1, 2, 3, 4).GetHashCode() == From(1, 2, 3, 4).GetHashCode());
            IsTrue(From(1, 2, 3, 4).GetHashCode() != From(2, 2, 3, 4).GetHashCode());

            // ==
            IsTrue(From(1, 2, 3, 4) == From(1, 2, 3, 4));
            IsFalse(From(1, 2, 3, 5) == From(1, 2, 3, 4));

            // !=
            IsFalse(From(1, 2, 3, 4) != From(1, 2, 3, 4));
            IsTrue(From(1, 2, 3, 5) != From(1, 2, 3, 4));

            // >
            IsFalse(From(1, 2, 3, 4) > From(1, 2, 3, 4));

            IsFalse(From(1, 2, 3, 4) > From(1, 2, 3, 5));
            IsFalse(From(1, 2, 3, 4) > From(1, 2, 4, 4));
            IsFalse(From(1, 2, 3, 4) > From(1, 3, 3, 4));
            IsFalse(From(1, 2, 3, 4) > From(2, 2, 3, 4));

            IsTrue(From(1, 2, 3, 5) > From(1, 2, 3, 4));
            IsTrue(From(1, 2, 4, 4) > From(1, 2, 3, 4));
            IsTrue(From(1, 3, 3, 4) > From(1, 2, 3, 4));
            IsTrue(From(2, 2, 3, 4) > From(1, 2, 3, 4));

            // >=
            IsTrue(From(1, 2, 3, 4) >= From(1, 2, 3, 4));

            IsFalse(From(1, 2, 3, 4) >= From(1, 2, 3, 5));
            IsFalse(From(1, 2, 3, 4) >= From(1, 2, 4, 4));
            IsFalse(From(1, 2, 3, 4) >= From(1, 3, 3, 4));
            IsFalse(From(1, 2, 3, 4) >= From(2, 2, 3, 4));

            IsTrue(From(1, 2, 3, 5) >= From(1, 2, 3, 4));
            IsTrue(From(1, 2, 4, 4) >= From(1, 2, 3, 4));
            IsTrue(From(1, 3, 3, 4) >= From(1, 2, 3, 4));
            IsTrue(From(2, 2, 3, 4) >= From(1, 2, 3, 4));

            // <
            IsFalse(From(1, 2, 3, 4) < From(1, 2, 3, 4));

            IsTrue(From(1, 2, 3, 4) < From(1, 2, 3, 5));
            IsTrue(From(1, 2, 3, 4) < From(1, 2, 4, 4));
            IsTrue(From(1, 2, 3, 4) < From(1, 3, 3, 4));
            IsTrue(From(1, 2, 3, 4) < From(2, 2, 3, 4));

            IsFalse(From(1, 2, 3, 5) < From(1, 2, 3, 4));
            IsFalse(From(1, 2, 4, 4) < From(1, 2, 3, 4));
            IsFalse(From(1, 3, 3, 4) < From(1, 2, 3, 4));
            IsFalse(From(2, 2, 3, 4) < From(1, 2, 3, 4));

            // <=
            IsTrue(From(1, 2, 3, 4) <= From(1, 2, 3, 4));

            IsTrue(From(1, 2, 3, 4) <= From(1, 2, 3, 5));
            IsTrue(From(1, 2, 3, 4) <= From(1, 2, 4, 4));
            IsTrue(From(1, 2, 3, 4) <= From(1, 3, 3, 4));
            IsTrue(From(1, 2, 3, 4) <= From(2, 2, 3, 4));

            IsFalse(From(1, 2, 3, 5) <= From(1, 2, 3, 4));
            IsFalse(From(1, 2, 4, 4) <= From(1, 2, 3, 4));
            IsFalse(From(1, 3, 3, 4) <= From(1, 2, 3, 4));
            IsFalse(From(2, 2, 3, 4) <= From(1, 2, 3, 4));

            void IsTrue(bool result)
            {
                if (!result)
                {
                    throw new ArgumentException("the expression is true.");
                }
            }

            void IsFalse(bool result)
            {
                if (result)
                {
                    throw new ArgumentException("the expression is not false.");
                }
            }
        }

        /// <summary>
        /// このインスタンスと指定したオブジェクトを比較し、並べ替え順序において、
        /// このインスタンスの位置が指定したオブジェクトの前、後ろ、または同じのいずれであるかを示す整数を返します.
        /// </summary>
        /// <param name="other">このインスタンスの比較する対象.</param>
        /// <returns>
        /// 0 より小さい値 このインスタンスの位置が other よりも前です.
        /// 0 このインスタンスの位置が、並べ替え順序において other と同じです.
        /// 0 より大きい値 このインスタンスの位置が other よりも後ろ、または other が null です.
        /// </returns>
        public int CompareTo(AppVersion other)
        {
            return Compare(this, other);
        }

        /// <summary>
        /// 比較元と等価であるか判別する.
        /// </summary>
        /// <param name="obj">比較元.</param>
        /// <returns>等価の場合 true. 等価でない場合、false.</returns>
        public override bool Equals(object obj)
        {
            if (obj is AppVersion other)
            {
                return Compare(this, other) == 0;
            }

            return false;
        }

        /// <summary>
        /// 既定のハッシュ関数.
        /// </summary>
        /// <returns>現在のオブジェクトのハッシュ コード.</returns>
        public override int GetHashCode()
        {
            HashCode hashCode = new ();
            hashCode.Add(this.MajorNum);
            hashCode.Add(this.MinorNum);
            hashCode.Add(this.BuildNum);
            hashCode.Add(this.RevisionNum);

            return hashCode.ToHashCode();
        }

        /// <summary>
        /// バージョン情報を取得する.
        /// </summary>
        /// <returns>バージョン情報.</returns>
        public override string ToString()
        {
            return this.ToString(".");
        }

        /// <summary>
        /// バージョン情報を取得する.
        /// </summary>
        /// <param name="separator">区切り文字.</param>
        /// <returns>バージョン情報.</returns>
        public string ToString(string separator)
        {
            return string.Join(separator, new[] { this.MajorNum, this.MinorNum, this.BuildNum, this.RevisionNum });
        }

        /// <summary>
        /// 右辺と左辺が等価であるか確認する.
        /// </summary>
        /// <param name="left">右辺.</param>
        /// <param name="right">左辺.</param>
        /// <returns>等価の場合 true. 等価でない場合、false.</returns>
        public static bool operator ==(AppVersion left, AppVersion right)
        {
            return Compare(left, right) == 0;
        }

        /// <summary>
        /// 左辺が右辺より大きなバージョンであるか確認する.
        /// </summary>
        /// <param name="left">左辺.</param>
        /// <param name="right">右辺.</param>
        /// <returns>左辺が右辺より大きなバージョンであれば true. それ以外は false.</returns>
        public static bool operator >(AppVersion left, AppVersion right)
        {
            return Compare(left, right) > 0;
        }

        /// <summary>
        /// 右辺と左辺が等価であるか、左辺が右辺より大きなバージョンであるか確認する.
        /// </summary>
        /// <param name="left">左辺.</param>
        /// <param name="right">右辺.</param>
        /// <returns>右辺と左辺が等価であるか、左辺が右辺より大きなバージョンであれば true. それ以外は false.</returns>
        public static bool operator >=(AppVersion left, AppVersion right)
        {
            return Compare(left, right) >= 0;
        }

        /// <summary>
        /// 右辺が左辺より大きなバージョンであるか確認する.
        /// </summary>
        /// <param name="left">左辺.</param>
        /// <param name="right">右辺.</param>
        /// <returns>右辺が左辺より大きなバージョンであれば true. それ以外は false.</returns>
        public static bool operator <(AppVersion left, AppVersion right)
        {
            return Compare(left, right) < 0;
        }

        /// <summary>
        /// 右辺と左辺が等価であるか、右辺が左辺より大きなバージョンであるか確認する.
        /// </summary>
        /// <param name="left">左辺.</param>
        /// <param name="right">右辺.</param>
        /// <returns>右辺と左辺が等価であるか、右辺が左辺より大きなバージョンであれば true. それ以外は false.</returns>
        public static bool operator <=(AppVersion left, AppVersion right)
        {
            return Compare(left, right) <= 0;
        }

        /// <summary>
        /// 右辺と左辺が非等価であるか確認する.
        /// </summary>
        /// <param name="left">右辺.</param>
        /// <param name="right">左辺.</param>
        /// <returns>非等価の場合 true. 非等価でない場合、false.</returns>
        public static bool operator !=(AppVersion left, AppVersion right)
        {
            return Compare(left, right) != 0;
        }

        /// <summary>
        /// 二つの AppVersion を比較する.
        /// </summary>
        /// <param name="left">右辺.</param>
        /// <param name="right">左辺.</param>
        /// <returns>
        /// 0 より小さい値： left の位置が other よりも前です.または left が null です.
        /// 0 ：left の位置が、並べ替え順序において right と同じです.
        /// 0 より大きい値： このleftの位置が right よりも後ろ、または right が null です.
        /// </returns>
        private static int Compare(AppVersion left, AppVersion right)
        {
            return (left, right) switch
            {
                (null, null) => 0,
                (null, AppVersion _) => -1,
                (AppVersion _, null) => 1,
                (AppVersion a, AppVersion b) => CompareInternal(a, b)
            };
        }

        /// <summary>
        /// 二つの AppVersion を比較する.
        /// </summary>
        /// <param name="left">右辺.</param>
        /// <param name="right">左辺.</param>
        /// <returns>
        /// 0 より小さい値： left の位置が other よりも前です.または left が null です.
        /// 0 ：left の位置が、並べ替え順序において right と同じです.
        /// 0 より大きい値： このleftの位置が right よりも後ろ、または right が null です.
        /// </returns>
        private static int CompareInternal(AppVersion left, AppVersion right)
        {
            return (right.MajorNum, right.MinorNum, right.BuildNum, right.RevisionNum) switch
            {
                (var majorNum, _, _, _) when majorNum < left.MajorNum => 1,
                (var majorNum, _, _, _) when majorNum > left.MajorNum => -1,
                (_, var minorNum, _, _) when minorNum < left.MinorNum => 1,
                (_, var minorNum, _, _) when minorNum > left.MinorNum => -1,
                (_, _, var buildNum, _) when buildNum < left.BuildNum => 1,
                (_, _, var buildNum, _) when buildNum > left.BuildNum => -1,
                (_, _, _, var revisionNum) when revisionNum < left.RevisionNum => 1,
                (_, _, _, var revisionNum) when revisionNum > left.RevisionNum => -1,
                _ => 0 // 等価
            };
        }
    }
}
