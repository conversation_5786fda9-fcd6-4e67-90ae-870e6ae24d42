{"JproRelease": {"Database": {"ChozaiDbConnectionString": "Data Source=localhost;Initial Catalog=chozaidb;User ID=7d9382940134a4ab16fd36db489dda10959b0008abacdd3aa9125a106ebe481102fe6fc0608cfc50fd7603d382fecef9;Password=************************************************************************************************;Connect Timeout=30;"}, "Deployment": {"SharedDirectory": "D:\\EXESHARE\\JPRO\\Server\\", "JproBaseDirectory": "D:\\system\\JPRO\\", "TempDirectory": "D:\\system\\JPRO\\Work\\", "DeploymentResultDirectory": "D:\\system\\JPRO\\JproDeployment\\result", "NginxDirectory": "D:\\system\\JPRO\\nginx\\", "ErrorRetryInterval": 30}, "Etc": {"DbDebounceInterval": 10, "FileDebounceInterval": 10, "FileRetryCount": 5, "FileRetryInitialInterval": 5, "FileRetryMaxInterval": 60}}, "Serilog": {"Using": ["SeriLog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Async"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Debug", "System": "Debug"}}, "WriteTo": [{"Name": "File", "Args": {"path": "JproDeploymentService-.log", "outputTemplate": "| {Timestamp:HH:mm:ss.fff} | {Level:u4} | {ProcessId:00} | {ThreadId:00} | {MessageId} | {Message:j} | {SourceContext} | {MemberName} | {LineNumber} | {MachineName} | {EnvironmentUserName} | {NewLine}{Exception}", "rollingInterval": "Day"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "| {Timestamp:yyyy/MM/dd HH:mm:ss.fff} | {Level:u4} | {ProcessId:00} | {ThreadId:00} | {MessageId} | {Message:j} | {SourceContext} | {MemberName} | {LineNumber} | {MachineName} | {EnvironmentUserName} | {NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithThreadId", "WithProcessId"]}}