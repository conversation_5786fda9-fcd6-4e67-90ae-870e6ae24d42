﻿// <copyright file="SelfInstallerOperation.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.SelfInstaller
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Runtime.CompilerServices;
    using System.ServiceProcess;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Cysharp.Diagnostics;
    using JproBackend.Common.Utility.EncryptionUtility;
    using JproBackend.Common.Utility.SystemDateTime;
    using JproBackend.JproBackCommon.Common.Base.Constants;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproBackend.JproBackCommon.Common.Utility.ConnectionStringUtility;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;
    using Microsoft.Data.SqlClient;
    using Microsoft.Extensions.Configuration;
    using Serilog;

    /// <summary>
    /// 自己インストール処理操作.
    /// JPROインストール機能を Windows Service 登録するための機能.
    /// </summary>
    internal class SelfInstallerOperation
    {
        /// <summary>
        /// <see cref="SelfInstallerOperation"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="environmentUtility">環境設定ユーティリティ.</param>
        /// <param name="configuration">実行モード.</param>
        /// <param name="logger">ロガー.</param>
        public SelfInstallerOperation(
            EnvironmentUtility environmentUtility,
            JproReleaseConfiguration configuration,
            FileLogger<SelfInstallerOperation> logger = null)
        {
            this.EnvironmentUtility = environmentUtility;
            this.Configuration = configuration;
            this.Logger = logger;
        }

        /// <summary>
        /// 環境設定ユーティリティを取得する.
        /// </summary>
        private EnvironmentUtility EnvironmentUtility { get; }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<SelfInstallerOperation> Logger { get; }

        /// <summary>
        /// 自己インストール処理.
        /// JPROインストール機能を Windows Service 登録する.
        /// </summary>
        /// <returns>この関数は戻り値のない非同期処理.</returns>
        public async Task Execute()
        {
            // サービスとして稼働するためのJPROリリース機能の設定ファイル情報を構築する
            this.ApplyServerSetting();

            // 静的キャッシュを更新する
            this.EnvironmentUtility.RefreshIniTableCache();

            // インストール済みのJPROリリース機能のサービス情報を取得する
            var servicesVersions = this.EnvironmentUtility.GetJproDeploymentServiceVersions();

            // インストール対象のアプリバージョンを取得する
            var currentPath = this.GetType().Assembly.Location;
            var appVersion = AppVersion.From(FileVersionInfo.GetVersionInfo(currentPath).FileVersion, ".");

            this.LogInformation($"JPRO リリース機能 ({appVersion}) の Windows サービス登録処理を開始します。");

            // 現在のコピー元のディレクトリ
            var srcPath = DirectoryAccessor.GetDirectoryName(currentPath);

            // コピー先のディレクトリ
            var distPath = Path.Combine(
                Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "JproDeployment"),
                $"v{appVersion}");

            // インストール先のディレクトリにコピーしました
            this.MoveToInstallPath(srcPath, distPath);
            this.LogInformation($"JPRO リリース機能 ({appVersion}) をインストール先にコピーしました ({distPath})。");

            // 設定ファイルを更新する
            await this.UpdateJsonSetting(distPath, appVersion);
            this.LogInformation($"JPRO リリース機能 ({appVersion}) に設定ファイルを更新しました。");

            // サービス登録する
            await this.RegisterJproDeploymentService(appVersion);
            this.LogInformation($"JPRO リリース機能 ({appVersion}) を Windows サービス登録しました。");

            // サービスを起動する
            this.StartDeploymentService(appVersion);
            this.LogInformation($"JPRO リリース機能 ({appVersion}) を起動しました。");

            // 起動済みの既存のサービスを停止する
            // リリース機能の自己インストール機能でこのメソッドが実行されている場合
            // 呼び出し元の起動済みの既存のサービスは本処理が正常に完了するまで待機状態になっているための
            // 先に新バージョンのインストールを開始しても良い
            await this.UninstallDeploymentService(servicesVersions);

            this.LogInformation($"JPRO リリース機能 ({appVersion}) の Windows サービス登録処理が完了しました。");

            // バージョン情報ログを出力する
            this.OutputDeploymentResult(appVersion);
        }

        /// <summary>
        /// サーバー固有設定の適用処理.
        /// 設定ファイルの接続文字列を初期化する.
        /// </summary>
        private void ApplyServerSetting()
        {
            // 接続文字列の localhost 設定を実行中の端末に変更する
            this.Configuration.Database.ChozaiDbConnectionString =
                this.ConvertLocalhostConnectionString(this.Configuration.Database.ChozaiDbConnectionString);
            this.Configuration.Database.BearChozaiDbConnectionString =
                this.ConvertLocalhostConnectionString(this.Configuration.Database.BearChozaiDbConnectionString);

            // サーバー固有の設定が存在しない場合、終了
            if (this.Configuration.Servers[Environment.MachineName] is not JproReleaseConfiguration jproConfig)
            {
                return;
            }

            // データベース
            if (jproConfig.Database is Database database)
            {
                this.Configuration.Database.ChozaiDbConnectionString = database.ChozaiDbConnectionString;
                this.Configuration.Database.BearChozaiDbConnectionString = database.BearChozaiDbConnectionString;
            }

            // リリース機能設定
            if (jproConfig.Deployment is Deployment deployment)
            {
                if (deployment.JproBaseDirectory is string jproBaseDirectory)
                {
                    this.Configuration.Deployment.JproBaseDirectory = jproBaseDirectory;
                }

                if (deployment.DeploymentResultDirectory is string deploymentResultDirectory)
                {
                    this.Configuration.Deployment.DeploymentResultDirectory = deploymentResultDirectory;
                }
            }
        }

        /// <summary>
        /// 接続文字列の localhost 設定を実行中の端末に変更する.
        /// </summary>
        /// <param name="connectionString">元になる接続文字列.</param>
        /// <returns>変更された接続文字列.</returns>
        private string ConvertLocalhostConnectionString(string connectionString)
        {
            var connectionStringBuilder = new SqlConnectionStringBuilder(connectionString);

            // ローカルホストでない場合、そのまま使用する
            if (connectionStringBuilder.DataSource.ToLower() != "localhost")
            {
                return connectionString.ToString();
            }

            // ホスト名を設定する
            connectionStringBuilder.DataSource = Environment.MachineName;
            return connectionStringBuilder.ToString();
        }

        /// <summary>
        /// JPROリリース機能削除処理.
        /// 既存の登録済みのサービスを停止します.
        /// </summary>
        /// <param name="servicesVersions">インストール済みのサービス情報.</param>
        /// <returns>この関数は戻り値のない非同期処理.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        private async Task UninstallDeploymentService(List<(ServiceController Servive, AppVersion Version)> servicesVersions)
        {
            // サービスを停止する
            foreach (var serviceVersion in servicesVersions)
            {
                if (serviceVersion.Servive.Status == ServiceControllerStatus.Running)
                {
                    this.LogInformation($"JPRO リリース機能 ({serviceVersion.Version}) を Windows サービスから停止します。");
                    serviceVersion.Servive.Stop();
                    this.LogInformation($"JPRO リリース機能 ({serviceVersion.Version}) を Windows サービスから停止しました。");
                }
            }

            // サービスを削除する
            foreach (var serviceVersion in servicesVersions)
            {
                // サービス名
                var serviceName = $"JproDeploymentService_v{serviceVersion.Version.ToString("_")}";

                // 削除コマンドを実行する
                var command = $@"sc delete {serviceName} ";
                await foreach (var result in ProcessX.StartAsync(command))
                {
                    this.LogInformation($"JPRO リリース機能 ({serviceVersion.Version}) を Windows サービスから削除します。");
                    this.LogInformation(result);
                    this.LogInformation($"JPRO リリース機能 ({serviceVersion.Version}) を Windows サービスから削除しました。");
                }
            }

            // ディレクトリの削除はインストール成功後に、定期実施する
        }

        /// <summary>
        /// インストールフォルダ展開処理.
        /// インストール先のディレクトリにコピーする.
        /// </summary>
        /// <param name="srcPath">コピー元のディレクトリ.</param>
        /// <param name="distPath">コピー先のディレクトリ.</param>
        private void MoveToInstallPath(string srcPath, string distPath)
        {
            // ファイルが存在する場合、削除する
            if (DirectoryAccessor.Exists(distPath))
            {
                DirectoryAccessor.DeleteDirectory(distPath);
            }

            // ファイルをコピーする
            this.CopyDirectory(srcPath, distPath);
        }

        /// <summary>
        /// ディレクトリコピー処理.
        /// ディレクトリを完全コピーする.
        /// </summary>
        /// <param name="srcDir">コピー元ディレクトリ.</param>
        /// <param name="distDir">コピー先ディレクトリ.</param>
        private void CopyDirectory(string srcDir, string distDir)
        {
            var dir = new DirectoryInfo(srcDir);

            // ディレクトリを作成する
            Directory.CreateDirectory(distDir);

            // ファイルをコピーする
            foreach (FileInfo file in dir.GetFiles())
            {
                string targetFilePath = Path.Combine(distDir, file.Name);
                file.CopyTo(targetFilePath);
            }

            // ディレクトリをコピーする
            DirectoryInfo[] dirs = dir.GetDirectories();
            foreach (DirectoryInfo subDir in dirs)
            {
                string newDistDir = Path.Combine(distDir, subDir.Name);
                this.CopyDirectory(subDir.FullName, newDistDir);
            }
        }

        /// <summary>
        /// JPROリリース機能設定ファイル更新処理.
        /// </summary>
        /// <param name="distPath">コピー先ディレクトリ.</param>
        /// <param name="appVersion">バージョン設定ファイル.</param>
        /// <returns>この関数は戻り値のない非同期処理.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("StyleCop.CSharp.ReadabilityRules", "SA1101:Prefix local calls with this", Justification = "<保留中>")]
        private async Task UpdateJsonSetting(string distPath, AppVersion appVersion)
        {
            var jsonPath = Path.Combine(distPath, ReleaseConstants.AppsettingsFileName);
            var appSetting = new JsonFile(jsonPath);

            // DB設定
            appSetting.SetValue("JproRelease.Database.ChozaiDbConnectionString", this.Configuration.Database.BearChozaiDbConnectionString);

            var jproConfig = this.Configuration.Servers[Environment.MachineName];

            // リリース機能設定
            if (jproConfig.Deployment is Deployment deployment)
            {
                var deplaymentKey = "JproRelease.Deployment.";
                ApplaySetting(deplaymentKey + nameof(deployment.SharedDirectory), deployment.SharedDirectory);
                ApplaySetting(deplaymentKey + nameof(deployment.JproBaseDirectory), deployment.JproBaseDirectory);
                ApplaySetting(deplaymentKey + nameof(deployment.TempDirectory), deployment.TempDirectory);
                ApplaySetting(deplaymentKey + nameof(deployment.DeploymentResultDirectory), deployment.DeploymentResultDirectory);
                ApplaySetting(deplaymentKey + nameof(deployment.ErrorRetryInterval), deployment.ErrorRetryInterval);
                ApplaySetting(deplaymentKey + nameof(deployment.NginxDirectory), deployment.NginxDirectory);
            }

            // その他設定
            if (jproConfig.Etc is Etc etc)
            {
                var etcKey = "JproRelease.Etc.";
                ApplaySetting(etcKey + nameof(etc.RegexTimeout), etc.RegexTimeout, ReleaseConstants.RegexTimeoutEtcSetting);
                ApplaySetting(etcKey + nameof(etc.FinalizeTimeout), etc.FinalizeTimeout, ReleaseConstants.FinalizeTimeoutEtcSetting);
                ApplaySetting(etcKey + nameof(etc.StaticCache), etc.StaticCache, ReleaseConstants.StaticCacheEtcSetting);
                ApplaySetting(etcKey + nameof(etc.DbDebounceInterval), etc.DbDebounceInterval, ReleaseConstants.DbDebounceIntervalEtcSetting);
                ApplaySetting(etcKey + nameof(etc.FileDebounceInterval), etc.FileDebounceInterval, ReleaseConstants.FileDebounceIntervalEtcSetting);
                ApplaySetting(etcKey + nameof(etc.UnusedPortRetryCount), etc.UnusedPortRetryCount, ReleaseConstants.UnusedPortRetryCountEtcSetting);
                ApplaySetting(etcKey + nameof(etc.UnusedPortRetryInterval), etc.UnusedPortRetryInterval, ReleaseConstants.UnusedPortRetryIntervalEtcSetting);
                ApplaySetting(etcKey + nameof(etc.SeflInstallTaskDelay), etc.SeflInstallTaskDelay, ReleaseConstants.SeflInstallTaskDelayEtcSetting);
                ApplaySetting(etcKey + nameof(etc.PreserveMaterials), etc.PreserveMaterials, ReleaseConstants.PreserveMaterialsEtcSetting);
            }

            // Serilog.WriteTo.0.Args.configure.0.Args.path : ログパス
            var logPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Log", "JproDeployment", $"JproDeployment-v{appVersion.ToString(".")}-.log");
            appSetting.SetValue("Serilog.WriteTo.0.Args.path", logPath);

            await appSetting.SaveFile(jsonPath);

            // 既定値または、指定された値でない場合、値を設定する
            void ApplaySetting<TValue>(string key, TValue value, TValue systemDefaultValue = default(TValue))
            {
                if (object.Equals(value, default(TValue)) || object.Equals(value, systemDefaultValue))
                {
                    return;
                }

                appSetting.SetValue(key, value);
            }
        }

        /// <summary>
        /// JPROリリース機能サービス登録処理.
        /// JPROリリース機能をサービス登録する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <returns>このメソッドは戻り値のない非同期処理.</returns>
        private async Task RegisterJproDeploymentService(AppVersion appVersion)
        {
            // サービス名
            var serviceName = $"JproDeploymentService_v{appVersion.ToString("_")}";

            // 表示名
            var displayName = $"JPROリリース機能 v{appVersion}";

            // インストール先
            var binPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "JproDeployment", $"v{appVersion}", "JproDeploymentService.exe");

            // ユーザー名
            var user = DbStaticCache.SysCommonIni.GetCurrentSettings()
                .First(ini => ini.DataDiv == ReleaseConstants.DataDivBackendUserName)?
                .CharValue;

            user = this.DecryptedText(user);

            // パスワード
            var password = DbStaticCache.SysCommonIni.GetCurrentSettings()
                .First(ini => ini.DataDiv == ReleaseConstants.DataDivBackendPassword)?
                .CharValue;

            password = this.DecryptedText(password);

            var command =
                $@"sc create " +
                $@"{serviceName} " +
                $@"displayname= ""{displayName}"" " +
                (string.IsNullOrEmpty(user) ? string.Empty : $@"obj= {user} ") +
                (string.IsNullOrEmpty(password) ? string.Empty : $@"password= {password} ") +
                $@"type= own " +
                $@"start= delayed-auto " +
                $@"binpath= ""{binPath}""";

            await foreach (var result in ProcessX.StartAsync(command))
            {
                this.LogDebug(result);
            }
        }

        /// <summary>
        /// JPROリリース機能サービス開始処理.
        /// 指定されたバージョンのサービスを起動する.
        /// </summary>
        /// <param name="appVersion">バージョン.</param>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        private void StartDeploymentService(AppVersion appVersion)
        {
            // サービス名
            var serviceName = $"JproDeploymentService_v{appVersion.ToString("_")}";

            // サービス
            var service = ServiceController.GetServices()
                .FirstOrDefault(service => service.ServiceName == serviceName);

            // サービスを起動する
            service?.Start();
        }

        /// <summary>
        /// 暗号化文字列復号処理.
        /// 暗号化されたID/パワードを復号化する.
        /// </summary>
        /// <param name="encryptText">暗号化された文字列.</param>
        /// <returns>復号化された文字列.</returns>
        private string DecryptedText(string encryptText)
        {
            if (string.IsNullOrWhiteSpace(encryptText))
            {
                return null;
            }

            try
            {
                using var memoryStream = new MemoryStream(Convert.FromHexString(encryptText));
                return EncryptionUtility.GetDecryptedText(
                    Constants.Base.ConnectionStringDecryptPassword,
                    memoryStream);
            }
            catch (Exception exception)
            {
                this.LogError(exception, "パスワードの復号化処理に失敗しました。");
                return null;
            }
        }

        /// <summary>
        /// 動作確認用に情報ログ出力する.
        /// 自己インストールモードでロガーが存在しない場合、既定のロガーで出力する.
        /// </summary>
        /// <param name="message">ログに記録するデバッグメッセージ.</param>
        /// <param name="memberName">(この引数はメッセージパラメータではありません) コンパイラが引数名を付与します.</param>
        /// <param name="filePath">(この引数はメッセージパラメータではありません) コンパイラがファイルパスを付与します.</param>
        /// <param name="lineNumber">(この引数はメッセージパラメータではありません) コンパイラが行番号を付与します.</param>
        private void LogInformation(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (this.Logger is not null)
            {
                this.Logger.InformationLog(message, null, memberName, filePath, lineNumber);
            }
            else
            {
                Log.Information(message);
            }
        }

        /// <summary>
        /// バージョン情報ログ出力処理.
        /// JPROリリース機能のインストールバージョンをログ出力する.
        /// </summary>
        /// <param name="version">インストールバージョン.</param>
        private void OutputDeploymentResult(AppVersion version)
        {
            try
            {
                var envUtil = new EnvironmentUtility(this.Configuration);
                (string pharmacyName, string pharmacyCd) = envUtil.GetPharmacyData();

                var resultFileContents = new StringBuilder();
                resultFileContents.AppendLine($"店舗コード\t{pharmacyCd}");
                resultFileContents.AppendLine($"店舗名\t{pharmacyName}");
                resultFileContents.AppendLine($"リリース日時\t{SystemDateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}");
                resultFileContents.AppendLine($"JPROリリース機能\tJPRODeploymentService\t{version}");

                if (!DirectoryAccessor.Exists(this.Configuration.Deployment.DeploymentResultDirectory))
                {
                    DirectoryAccessor.CreateDirectory(this.Configuration.Deployment.DeploymentResultDirectory);
                }

                var resultFilePath = Path.Combine(
                    this.Configuration.Deployment.DeploymentResultDirectory,
                    $"JPRODeploymentService_{pharmacyCd}_v{version.ToString("_")}_{pharmacyName}.txt");

                using var resultFile = new StreamWriter(resultFilePath, false, Encoding.GetEncoding("shift_jis"));
                resultFile.Write(resultFileContents.ToString());
            }
            catch (Exception exception)
            {
                this.LogError(exception, "リリース結果出力ファイルの生成に失敗しました。");
            }
        }

        /// <summary>
        /// 動作確認用にデバッグログ出力する.
        /// 自己インストールモードでロガーが存在しない場合、既定のロガーで出力する.
        /// </summary>
        /// <param name="message">ログに記録するデバッグメッセージ.</param>
        /// <param name="memberName">(この引数はメッセージパラメータではありません) コンパイラが引数名を付与します.</param>
        /// <param name="filePath">(この引数はメッセージパラメータではありません) コンパイラがファイルパスを付与します.</param>
        /// <param name="lineNumber">(この引数はメッセージパラメータではありません) コンパイラが行番号を付与します.</param>
        private void LogDebug(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (this.Logger is not null)
            {
                this.Logger.DebugLog(message, null, memberName, filePath, lineNumber);
            }
            else
            {
                Log.Debug(message);
            }
        }

        /// <summary>
        /// 動作確認用にエラーログ出力する.
        /// 自己インストールモードでロガーが存在しない場合、既定のロガーで出力する.
        /// </summary>
        /// <param name="exception">例外.</param>
        /// <param name="message">ログに記録するデバッグメッセージ.</param>
        /// <param name="memberName">(この引数はメッセージパラメータではありません) コンパイラが引数名を付与します.</param>
        /// <param name="filePath">(この引数はメッセージパラメータではありません) コンパイラがファイルパスを付与します.</param>
        /// <param name="lineNumber">(この引数はメッセージパラメータではありません) コンパイラが行番号を付与します.</param>
        private void LogError(
            Exception exception,
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (this.Logger is not null)
            {
                this.Logger.ErrorLog(exception, message, null, memberName, filePath, lineNumber);
            }
            else
            {
                Log.Error(exception, message);
            }
        }
    }
}
