﻿// <copyright file="CommandLineOperation.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service.Operation
{
    using System;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Cysharp.Diagnostics;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproDeploymentService.Common.Entity;
    using Newtonsoft.Json;

    /// <summary>
    /// CommandLine操作クラス.
    /// CommandLineのインストールを制御するための処理の実装.
    /// </summary>
    internal class CommandLineOperation
    {
        /// <summary>
        /// <see cref="CommandLineOperation"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="logger">ロガー.</param>
        /// <param name="configuration">設定ファイル.</param>
        public CommandLineOperation(
            FileLogger<CommandLineOperation> logger,
            JproReleaseConfiguration configuration)
        {
            this.Logger = logger;
            this.Configuration = configuration;
        }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<CommandLineOperation> Logger { get; }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; }

        /// <summary>
        /// JPROバックエンドコマンドライン開始処理.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <param name="port">a.</param>
        /// <returns>このメソッドは戻り値のない非同期処理.</returns>
        public async Task StartJproBackend(AppVersion appVersion, int port)
        {
            try
            {
                // JproBackend.exe のリネームとバッチファイル作成
                var serviceName = $"JproBackend_v{appVersion.ToString("_")}.exe";
                var workingDirectory = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend", $"v{appVersion}");
                var binPath = Path.Combine(workingDirectory, serviceName);
                File.Move(Path.Combine(workingDirectory, "JproBackend.exe"), Path.Combine(workingDirectory, serviceName));

                // バッチファイルを作成（動作確認済みのアプローチを維持）
                string runScript = $@"start /min /d ""{workingDirectory}"" {serviceName}";
                string batFileName = Path.Combine(workingDirectory, serviceName.Replace(".exe", ".bat"));
                string fullPathToBatFile = Path.Combine(workingDirectory, batFileName);
                File.WriteAllText(fullPathToBatFile, runScript);

                // ステップ 1: スケジュールされたタスクを作成する（管理者権限で実行、ユーザーログオン不要でバックグラウンド実行）
                string taskName = "StartJproBackend";
                string schTasksCreateCmd = $@"schtasks /Create /RU jpro-dev\administrator /RP JPROlz51bca! /TN ""{taskName}"" /SC ONSTART /TR ""{fullPathToBatFile}"" /RL HIGHEST /IT /F";

                // コマンドを実行してスケジュールされたタスクを作成します
                await this.ExecuteCommandAsync(schTasksCreateCmd);

                // ステップ 2: 必要に応じて、スケジュールされたタスクをすぐに開始します
                string schTasksStartCmd = $@"schtasks /run /tn ""{taskName}""";

                // コマンドを実行してスケジュールされたタスクを開始します
                await this.ExecuteCommandAsync(schTasksStartCmd);

                // JproBackend のテスト
                string payload = JsonConvert.SerializeObject(
                    new
                    {
                        EmpId = "00001",
                        Password = "test1!!!",
                        Hostname = "test",
                    });

                var url = $"http://localhost:{port}/api/sc/sc010/scsc010001a/identity/login";

                // リトライ回数：3回
                for (int i = 0; i < 3; i++)
                {
                    await Task.Delay(2000);
                    var result = await this.TestJproBackend(url, payload);
                    if (result)
                    {
                        this.Logger.DebugLog($"{serviceName}を正常に開始しました.");
                        break;
                    }
                    else
                    {
                        // コマンドラインから JPRO を起動します
                        await this.ExecuteCommandAsync(schTasksStartCmd);
                        this.Logger.DebugLog($"{serviceName}開始試行: {i}回.");
                    }
                }

            }
                catch (Exception ex)
            {
                this.Logger.WarningLog(ex.Message);
            }
        }

        /// <summary>
        /// JPROバックエンドコマンドライン中止処理.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        public void StopJproBackend(AppVersion appVersion)
        {
            try
            {
                // サービス名
                var serviceName = $"JproBackend_v{appVersion.ToString("_")}";

                Process[] processes = Process.GetProcessesByName(serviceName);
                if (processes.Any())
                {
                    foreach (var process in processes)
                    {
                        // Stop process.
                        this.Logger.DebugLog($"Stop {serviceName}.");
                        process.Kill();
                    }
                }

                // スケジュールタスクも削除する
                try
                {
                    string taskName = "StartJproBackend";
                    string deleteTaskCmd = $@"schtasks /delete /tn ""{taskName}"" /f";
                    this.ExecuteCommandAsync(deleteTaskCmd).GetAwaiter().GetResult();
                    this.Logger.DebugLog($"Scheduled task {taskName} deleted.");
                }
                catch (Exception taskEx)
                {
                    this.Logger.DebugLog($"Failed to delete scheduled task: {taskEx.Message}");
                }
            }
            catch (Exception ex)
            {
                this.Logger.WarningLog(ex.Message);
            }
        }

        /// <summary>
        /// コマンドを実行する.
        /// </summary>
        /// <param name="command">コマンド.</param>
        /// <returns>このメソッドは戻り値のない非同期処理.</returns>
        private async Task ExecuteCommandAsync(string command)
        {
            ProcessStartInfo psi = new ProcessStartInfo("cmd.exe", $"/c {command}")
            {
                CreateNoWindow = true,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
            };

            using (Process process = Process.Start(psi))
            {
                process.WaitForExit(); // Wait for the command to complete
                string output = await process.StandardOutput.ReadToEndAsync(); // Capture the output
                string error = await process.StandardError.ReadToEndAsync(); // Capture any errors

            // Optionally, log the output and error for debugging
                this.Logger.DebugLog($"Output: {output}");
                this.Logger.DebugLog($"Error: {error}");
            }
        }

        /// <summary>
        /// JPRO起動を確認する.
        /// </summary>
        /// <param name="url">JPROのログインURL.</param>
        /// <param name="payload">ペイロード.</param>
        /// <returns>JPROが正常に起動されているかを判定する値を返す.</returns>
        /// 成功した場合、true.
        /// 失敗した場合、false.
        private async Task<bool> TestJproBackend(string url, string payload)
        {
            // HttpClientのインスタンスを作成する.
            using (HttpClient client = new HttpClient())
            {
                // JSONデータでHttpContentを作成する
                HttpContent content = new StringContent(payload, Encoding.UTF8, "application/json");

                try
                {
                    // JPROにPOSTリクエストを送信する.
                    HttpResponseMessage response = await client.PostAsync(url, content);

                    // リクエストが成功したかを確認する
                    if (response.IsSuccessStatusCode)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }
    }
}
