{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos202502\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos202502\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService.csproj": {"version": "0.0.0.7", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos202502\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService.csproj", "projectName": "JproDeploymentService", "projectPath": "C:\\Users\\<USER>\\source\\repos202502\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos202502\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[11.0.1, )"}, "CommandLineParser": {"target": "Package", "version": "[2.9.1, )"}, "Kana.NET": {"target": "Package", "version": "[1.0.6, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.7, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[6.0.7, )"}, "ProcessX": {"target": "Package", "version": "[1.5.5, )"}, "Serilog.Enrichers.AssemblyName": {"target": "Package", "version": "[1.0.9, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.2.0, )"}, "Serilog.Enrichers.Memory": {"target": "Package", "version": "[1.0.4, )"}, "Serilog.Enrichers.Process": {"target": "Package", "version": "[2.0.2, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Exceptions": {"target": "Package", "version": "[8.3.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[1.5.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.200\\RuntimeIdentifierGraph.json"}}}}}