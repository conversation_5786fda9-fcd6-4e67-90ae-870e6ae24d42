﻿// <copyright file="ApplicationEntry.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Bootstrap
{
    using System;
    using System.Diagnostics;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using Cysharp.Diagnostics;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproDeploymentService.Common.Bootstrap;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;
    using JproDeploymentService.SelfInstaller;
    using Microsoft.Extensions.Hosting;
    using Serilog;
    using Serilog.Events;

    /// <summary>
    /// プログラム起動処理クラス.
    /// </summary>
    public static class ApplicationEntry
    {
        /// <summary>
        /// プログラム起動処理.
        /// コマンドライン引数を判別してサービス起動または自己インストールモードの実行を行う.
        /// </summary>
        /// <param name="args">アプリケーションエントリポイントで指定された引数.</param>
        [SuppressMessage("Critical Security Hotspot", "S4792:Make sure that this logger's configuration is safe", Justification = "LoggerConfiguration の設定内容についてはレビュー済み")]
        public static void JproReleaseMain(string[] args)
        {
            // コマンドライン解析
            var optionData = CommandLine.ParseCommandLine(args);

            // 解析結果
            var installMode = optionData.InstallMode ? "Install" : "Service";
            var developmentMode = optionData.DevelopmentMode ? "Development" : "Production";

            // 設定ファイルの読み込み
            var config = InternalActivator.CreateConfiguration(developmentMode, false);

            // 初期化中にロガーを参照できないため、Serilog のグローバルロガーを使用する.
            Log.Logger = new LoggerConfiguration()
               .ReadFrom
               .Configuration(config)
               .CreateLogger();

            var currentVersion = AppVersion.From(
                FileVersionInfo.GetVersionInfo(typeof(ApplicationEntry).Assembly.Location).FileVersion, ".");
            var machineName = Environment.MachineName;

            Log.Information("JPRO リリース機能を起動します。");
            Log.Information($"バージョン： {currentVersion}");
            Log.Information($"起動モード： {installMode}");
            Log.Information($"実行レベル： {developmentMode}");
            Log.Information($"インストールサーバー名： {machineName}");

            // 設定ファイル
            var jproConfig = config.ToJproReleaseConfiguration();

            // 環境情報
            var envUtil = new EnvironmentUtility(jproConfig);

            AppVersion serviceVersion = envUtil.GetJproServiceVersion();
            AppVersion placeVersion = envUtil.GetJproPlacedVersion();
            Log.Information($"展開済みの最新バージョン： {placeVersion?.ToString() ?? "なし"}");
            Log.Information($"サービスの最新バージョン： {serviceVersion?.ToString() ?? "なし"}");

            try
            {
                // 稼働モードで実行処理を制御
                if (optionData.InstallMode)
                {
                    Log.Information("自己インストールモードで稼働します。");
                    Log.Information($"JRPOルートディレクトリ ({nameof(jproConfig.Deployment.JproBaseDirectory)})： {jproConfig.Deployment.JproBaseDirectory}");
                    Log.Information($"一時作業ディレクトリ ({nameof(jproConfig.Deployment.TempDirectory)})： {jproConfig.Deployment.TempDirectory}");

                    var selfInstaller = new SelfInstallerOperation(envUtil, jproConfig);
                    selfInstaller.Execute().GetAwaiter().GetResult();
                }
                else
                {
                    Log.Information("通常サービスモードで稼働します。");
                    Log.Information($"配信先ディレクトリ ({nameof(jproConfig.Deployment.SharedDirectory)})： {jproConfig.Deployment.SharedDirectory}");
                    Log.Information($"JRPOルートディレクトリ ({nameof(jproConfig.Deployment.JproBaseDirectory)})： {jproConfig.Deployment.JproBaseDirectory}");
                    Log.Information($"一時作業ディレクトリ ({nameof(jproConfig.Deployment.TempDirectory)})： {jproConfig.Deployment.TempDirectory}");
                    Log.Information($"エラー発生時リトライ間隔(秒) ({nameof(jproConfig.Deployment.ErrorRetryInterval)})： {jproConfig.Deployment.ErrorRetryInterval}");

                    // サービス起動 (.NET ホストとして起動する)
                    Host.CreateDefaultBuilder(args)
                        .UseWindowsService()
                        .UseSerilog()
                        .ConfigureServices((context, services) =>
                        {
                            // .NET ホストにサービス (Entity Framewrok の DBコンテキストなど) を登録する
                            new Startup(developmentMode).ConfigureServices(
                                context,
                                services,
                                config.ToJproReleaseConfiguration());
                        })
                        .Build()
                        .Run();
                }
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "JPRO リリース機能がエラーで中断しました。");
            }
            finally
            {
                Log.Information("JPRO リリース機能を終了します。");
                Log.CloseAndFlush();
            }
        }
    }
}
