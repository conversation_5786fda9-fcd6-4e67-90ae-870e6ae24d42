﻿// <copyright file="FileOperation.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service.Operation
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.ServiceProcess;
    using System.Text;
    using System.Threading.Tasks;
    using JproBackend.Common.Utility.ArchiveUtility;
    using JproBackend.Common.Utility.SystemDateTime;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproBackend.JproBackCommon.Common.Utility.FileAccess;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;

    /// <summary>
    /// ファイル処理操作クラス.
    /// リリース作業で実施するファイル関係の処理の実装.
    /// </summary>
    internal class FileOperation
    {
        /// <summary>
        /// <see cref="FileOperation"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="logger">ロガー.</param>
        /// <param name="environmentUtilLogger">環境設定ライブラリのロガー.</param>
        /// <param name="configuration">設定ファイル.</param>
        public FileOperation(
            FileLogger<FileOperation> logger,
            JproReleaseConfiguration configuration)
        {
            this.Logger = logger;
            this.Configuration = configuration;
        }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<FileOperation> Logger { get; }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; }

        /// <summary>
        /// リリース資材確認処理（リトライ機能付き）.
        /// 配信先ディレクトリに対象のアプリバージョンのzipファイルが存在するか確認を行い
        /// ファイルが存在するなら、zip を展開したテンポラリフォルダを応答する.
        /// ファイルが見つからない場合、設定された回数だけリトライを行う.
        /// </summary>
        /// <param name="appVersion">アプリバージョン.</param>
        /// <returns>テンポラリフォルダに展開されたパス.対象がない場合、null を応答する.</returns>
        public async Task<string> CopyMaterialZipWithRetry(List<AppVersion> appVersion)
        {
            for (int attempt = 1; attempt <= this.Configuration.Etc.FileRetryCount; attempt++)
            {
                // 既存のメソッドを試行
                var result = this.CopyMaterialZip(appVersion);
                if (result != null)
                {
                    if (attempt > 1)
                    {
                        this.Logger.InformationLog($"JproRelease.zip が {attempt} 回目の試行で正常に検出されました。");
                    }
                    return result; // 成功!
                }

                // 最後の試行でない場合、待機してリトライ
                if (attempt < this.Configuration.Etc.FileRetryCount)
                {
                    var delay = Math.Min(
                        this.Configuration.Etc.FileRetryInitialInterval * (int)Math.Pow(2, attempt - 1),
                        this.Configuration.Etc.FileRetryMaxInterval
                    ) * 1000;

                    this.Logger.InformationLog($"JproRelease.zip がまだ準備できていません。{delay/1000} 秒後にリトライします (試行 {attempt}/{this.Configuration.Etc.FileRetryCount})");
                    await Task.Delay(delay);
                }
            }

            this.Logger.WarningLog($"JproRelease.zip が {this.Configuration.Etc.FileRetryCount} 回の試行後も利用できませんでした。");
            return null;
        }

        /// <summary>
        /// リリース資材確認処理.
        /// 配信先ディレクトリに対象のアプリバージョンのzipファイルが存在するか確認を行い
        /// ファイルが存在するなら、zip を展開したテンポラリフォルダを応答する.
        /// </summary>
        /// <param name="appVersion">アプリバージョン.</param>
        /// <returns>テンポラリフォルダに展開されたパス.対象がない場合、null を応答する.</returns>
        public string CopyMaterialZip(List<AppVersion> appVersion)
        {
            // 配信先ディレクトリ
            var materialPath = Path.Combine(this.Configuration.Deployment.SharedDirectory, ReleaseConstants.MaterialFileNameJproRelease);

            // ファイルが存在しない場合、終了
            if (!this.TestFile(materialPath))
            {
                this.Logger.InformationLog($"リリース用資材 ({materialPath}) が配置されていません。");
                return null;
            }

            // テンポラリフォルダパス
            var tempDirectory = Path.Combine(
                this.Configuration.Deployment.TempDirectory,
                Guid.NewGuid().ToString().Replace("-", string.Empty));

            // 処理終了後にテンポラリフォルダパスを削除するかのフラグ
            // 処理に成功しない限り true にならない
            var preserveTempFolder = false;

            try
            {
                // テンポラリフォルダにzip資材を展開
                DirectoryAccessor.CreateDirectory(tempDirectory);

                var tempFilePath = Path.Combine(tempDirectory, ReleaseConstants.MaterialFileNameJproRelease); // テンポラリへの zip のパス
                var workDirPath = Path.Combine(tempDirectory, Guid.NewGuid().ToString().Replace("-", string.Empty)); // zip の展開先

                // テンポラリフォルダに zip を移動する
                File.Copy(materialPath, tempFilePath);

                // zip を展開する
                ArchiveUtility.ExpandArchive(tempFilePath, workDirPath);

                // JPROテンポラリ展開フォルダに配置されたリリースバージョンファイルのバージョンを取得する
                if (this.GetReleaseVersionFileVersion(workDirPath) is not AppVersion fileVersion)
                {
                    this.Logger.InformationLog($"バージョン定義ファイル ({Path.Combine(workDirPath, ReleaseConstants.JproVersionFileName)}) が配置されていません。");
                    return null;
                }

                // バージョンが一致しない場合、パス
                if (!appVersion.Exists(version => fileVersion == version))
                {
                    var dbVersion = string.Join(",", appVersion.Select(version => version.ToString()));
                    this.Logger.InformationLog($"バージョン定義ファイルのバージョンが一致しません。ファイルのバージョン {fileVersion} DBのバージョン {dbVersion}");
                    return null;
                }

                var appSettingPath = Path.Combine(workDirPath, "JproBackend", ReleaseConstants.AppsettingsFileName);
                if (!FileAccessor.Exists(appSettingPath))
                {
                    this.Logger.InformationLog($"リリース資材に設定ファイル ({appSettingPath}) が存在しません。");
                    return null;
                }

                // ファイルが存在する場合、zip 展開されたファイルパスを返却する.
                preserveTempFolder = true;
                return workDirPath;
            }
            finally
            {
                if (!this.Configuration.Etc.PreserveMaterials &&
                    !preserveTempFolder &&
                    DirectoryAccessor.Exists(tempDirectory))
                {
                    // テンポラリフォルダを削除する
                    DirectoryAccessor.DeleteDirectory(tempDirectory);
                }
            }
        }

        /// <summary>
        /// バージョン定義ファイルのリリースバージョン取得処理.
        /// </summary>
        /// <param name="workDirPath">リリース資材の一時展開パス.</param>
        /// <returns>バージョン定義ファイル上のバージョン定義.</returns>
        public AppVersion GetReleaseVersionFileVersion(string workDirPath)
        {
            // バージョン定義ファイル
            var jproVersionFilePath = Path.Combine(workDirPath, ReleaseConstants.JproVersionFileName);

            // バージョン定義ファイルが zip に存在しない場合、終了
            if (!FileAccessor.Exists(jproVersionFilePath))
            {
                return null;
            }

            // ファイルが存在する場合読み込みする
            var jsonFile = new JsonFile(jproVersionFilePath);
            return AppVersion.From(jsonFile.GetValue<string>("Version"), ".");
        }

        /// <summary>
        /// リリース資材削除処理.
        /// 配信先に存在する zip ファイルを削除する.
        /// </summary>
        public void RemoveMatrialZip()
        {
            // 配信先ディレクトリ
            var materialPath = Path.Combine(this.Configuration.Deployment.SharedDirectory, ReleaseConstants.MaterialFileNameJproRelease);

            // 配信先に存在する zip ファイルを削除する
            if (FileAccessor.Exists(materialPath))
            {
                FileAccessor.Delete(materialPath);
            }
        }

        /// <summary>
        /// JPROバックエンド設定ファイル更新処理.
        /// テンポラリパスに展開されたインストール資材のバックエンド設定ファイルについてシステム固有の設定値を更新する.
        /// </summary>
        /// <param name="workDirPath">インストール資材の展開パス.</param>
        /// <param name="port">ポート.</param>
        /// <param name="version">バージョン情報.</param>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        public async Task UpdateBackendAppSetting(string workDirPath, int port, AppVersion version)
        {
            // 設定ファイルの更新
            // Kestrel.Endpoints.Http.Url : 待ち受けポート
            // JPRO.ReleaseVersion : バージョン
            // JPRO.Frontend.FrontEndLogPath : フロントエンド保存パス
            // JPRO.Database.ChozaiDbConnectionString: 調剤DBの設定値
            // JPRO.Session.SqlServerCacheSetting.ConnectionString : (キャッシュデータ)調剤DBの設定値
            var appSettingPath = Path.Combine(workDirPath, "JproBackend", ReleaseConstants.AppsettingsFileName);
            var appSetting = new JsonFile(appSettingPath);
            appSetting.SetValue("Kestrel.Endpoints.Http.Url", $"http://localhost:{port}");
            appSetting.SetValue("JPRO.ReleaseVersion", version.ToString());
            appSetting.SetValue("JPRO.Frontend.FrontEndLogPath", Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Log", "Frontend"));
            appSetting.SetValue("JPRO.Database.ChozaiDbConnectionString", this.Configuration.Database.BearChozaiDbConnectionString);
            appSetting.SetValue("JPRO.Session.SqlServerCacheSetting.ConnectionString", this.Configuration.Database.BearChozaiDbConnectionString);

            // Serilog.WriteTo.0.Args.configure.0.Args.path : ログパス
            var logPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Log", "Backend", $"JproBackend-v{version.ToString(".")}-.log");
            appSetting.SetValue("Serilog.WriteTo.0.Args.configure.0.Args.path", logPath);

            // ファイル書き込みを行う
            await appSetting.SaveFile(appSettingPath);
        }

        /// <summary>
        /// JPROフロントエンドベースURL更新処理.
        /// テンポラリパスに展開されたインストール資材のフロントエンドの Base Path についてシステム固有の設定値を更新する.
        /// </summary>
        /// <param name="workDirPath">インストール資材の展開パス.</param>
        /// <param name="version">バージョン情報.</param>>
        public void UpdateFrontendBasePath(string workDirPath, AppVersion version)
        {
            var indexHtmlPath = Path.Combine(workDirPath, "JproFrontend", "index.html");

            var utf8 = new UTF8Encoding(false);
            var content = File.ReadAllText(indexHtmlPath, utf8).Replace(
                @$"<base href=""/"">",
                @$"<base href=""/v{version}/"">");

            using var file = new StreamWriter(indexHtmlPath, false, utf8);
            file.Write(content);
        }

        /// <summary>
        /// Webサーバー設定更新処理.
        /// Nginx の設定を更新する.
        /// </summary>
        /// <param name="appVersion">バージョン情報.</param>
        /// <param name="port">ポート.</param>
        /// <param name="environmentUtility">環境設定情報.</param>
        public void CreateNginxSetting(AppVersion appVersion, int port, EnvironmentUtility environmentUtility)
        {
            // 設定ファイル配置場所
            var configDir = Path.Combine(
                this.Configuration.Deployment.JproBaseDirectory,
                "Frontend\\nginx");

            var contents = new StringBuilder();
            contents.AppendLine(RootRedirectNgixConfig
                .Replace("<<VERSION>>", $"v{appVersion}")
                .Replace("<<PORT>>", port.ToString()));

            // http 版の設定ファイルに保存する
            var nginxHttpConfigPath = Path.Combine(
                configDir,
                $"jpro-http.nginx.{appVersion.ToString("_")}.conf");

            using var httpConfigFile = new StreamWriter(nginxHttpConfigPath, false, new UTF8Encoding(false));
            httpConfigFile.Write(contents.ToString());

            var versionRootValue =
              this.Configuration.Deployment.JproBaseDirectory
                + (this.Configuration.Deployment.JproBaseDirectory.EndsWith('/') ? string.Empty : "/")
                + "Frontend";

            // 過去のバージョンの設定を定義する
            environmentUtility.GetServiceApiPort()
                .Where(versionPort => versionPort.version != appVersion)
                .OrderBy(versionPort => versionPort.version)
                .ToList()
                .ForEach(versionPort =>
                {
                    contents.AppendLine(VesionNgixConfig
                        .Replace("<<VERSION>>", $"v{versionPort.version.ToString()}")
                        .Replace("<<PORT>>", versionPort.port.ToString())
                        .Replace("<<LOCATION_INDEX>>", $"@index_{versionPort.version.ToString("_")}")
                        .Replace("<<VERSION_ROOT>>", versionRootValue));
                });

            // 最新バージョンの定義を設定する
            contents.AppendLine(VesionNgixConfig
                .Replace("<<VERSION>>", $"v{appVersion}")
                .Replace("<<PORT>>", port.ToString())
                .Replace("<<LOCATION_INDEX>>", $"@index_{appVersion.ToString("_")}")
                .Replace("<<VERSION_ROOT>>", versionRootValue));

            // https 版の設定ファイルに保存する
            var nginxHttpsConfigPath = Path.Combine(
                this.Configuration.Deployment.JproBaseDirectory,
                "Frontend\\nginx",
                $"jpro-https.nginx.{appVersion.ToString("_")}.conf");

            using var httpsConfigFile = new StreamWriter(nginxHttpsConfigPath, false, new UTF8Encoding(false));
            httpsConfigFile.Write(contents.ToString());
        }

        /// <summary>
        /// インストールフォルダ展開処理.
        /// テンポラリパスに展開されたインストール資材を正規のインストールパスへ展開する.
        /// </summary>
        /// <param name="workDirPath">インストール資材の展開パス.</param>
        /// <param name="version">バージョン情報.</param>
        public void MoveToInstallPath(string workDirPath, AppVersion version)
        {
            // 現在のワーキングディレクトリ
            var fromBackendPath = Path.Combine(workDirPath, "JproBackend");
            var fromFrontendPath = Path.Combine(workDirPath, "JproFrontend");

            // 展開先のディレクトリ
            var toBackendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend", $"v{version}");
            var toFrontendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Frontend", $"v{version}");

            // 展開先にディレクトリが存在する場合、削除しておく
            PrepareDirectory(toBackendPath);
            PrepareDirectory(toFrontendPath);

            // ファイルをコピーする
            Directory.Move(fromBackendPath, toBackendPath);
            Directory.Move(fromFrontendPath, toFrontendPath);

            // フォルダをセットアップする
            void PrepareDirectory(string path)
            {
                if (DirectoryAccessor.Exists(path))
                {
                    DirectoryAccessor.DeleteDirectory(path);
                }
            }
        }

        /// <summary>
        /// 停止通知ファイル配置処理.
        /// 停止先のサービスのディレクトリにサービス停止通知ファイルを配置する.
        /// </summary>
        /// <param name="nextVersion">新しいバージョン.</param>
        /// <param name="currentVersion">通知を行う停止対象のバージョン.</param>
        /// <param name="terminateTime">停止予告日時.</param>
        /// <returns>この関数は戻り値のない非同期処理.</returns>
        public async Task CreateTerminateNotifyFile(AppVersion nextVersion, AppVersion currentVersion, DateTime terminateTime)
        {
            // 展開先のディレクトリ
            var backendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend", $"v{currentVersion}");

            // ディレクトリが存在しないなら終了
            if (!DirectoryAccessor.Exists(backendPath))
            {
                return;
            }

            // 配置用のパスが無いなら作成
            var terminationNotifyPath = Path.Combine(backendPath, "TerminationNotify");
            if (!DirectoryAccessor.Exists(terminationNotifyPath))
            {
                DirectoryAccessor.CreateDirectory(terminationNotifyPath);
            }

            // 通知ファイル
            var terminationFilePath = Path.Combine(terminationNotifyPath, "termination.json");

            var json = new JsonFile();
            json.SetValue("nextVersion", nextVersion.ToString());
            json.SetValue("terminationTime", terminateTime.ToString("yyyy/MM/dd HH:mm:ss"));
            await json.SaveFile(terminationFilePath);
        }

        /// <summary>
        /// インストールフォルダ削除処理.
        /// インストールパスへ展開されているファイルを削除する.
        /// </summary>
        /// <param name="version">バージョン情報.</param>
        public void DeleteInstallPath(AppVersion version)
        {
            // 展開先のディレクトリ
            var backendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend", $"v{version}");
            var frontendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Frontend", $"v{version}");

            // 展開先にディレクトリが存在する場合、削除しておく
            SafeDeleteDirectory(backendPath);
            SafeDeleteDirectory(frontendPath);

            void SafeDeleteDirectory(string path)
            {
                if (DirectoryAccessor.Exists(path))
                {
                    DirectoryAccessor.DeleteDirectory(path);
                }
            }
        }

        /// <summary>
        /// 作業用フォルダ削除処理.
        /// リリース済みのワークディレクトリを削除する.
        /// </summary>
        /// <param name="workDirPath">ワークディレクトリ.</param>
        public void DeleteWorkDirPath(string workDirPath)
        {
            try
            {
                DirectoryAccessor.DeleteDirectory(
                    DirectoryAccessor.GetDirectoryName(workDirPath));
            }
            catch
            {
                // noop
            }
        }

        /// <summary>
        /// ワークディレクトリの定期削除処理.
        /// 日次の再起動時にリリース用にワークファイルが残っている場合に消去する
        /// リリース済みのワークディレクトリを削除する.
        /// </summary>
        public void DeleteReleaseTempDir()
        {
            // GUID にマッチする正規表現
            var regex = new System.Text.RegularExpressions.Regex("([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})");

            // リリースファイルが存在するフォルダが削除対象
            // Configuration.Deployment.SharedDirectory/GUID/JproRelease.zip
            // Configuration.Deployment.SharedDirectory/GUID/JproDeploymentService.zip
            var releaseTempDir = Directory.GetDirectories(this.Configuration.Deployment.SharedDirectory)
                .Where(path => regex.IsMatch(Path.GetFileName(path)))
                .Where(path =>
                    File.Exists(Path.Combine(path, ReleaseConstants.MaterialFileNameJproRelease)) ||
                    File.Exists(Path.Combine(path, ReleaseConstants.MaterialFileNameJproDeploymentService)))
                .ToList();

            releaseTempDir.ForEach(path =>
            {
                this.Logger.DebugLog("削除対象の一時フォルダパス {0}");
                DirectoryAccessor.DeleteDirectory(path);
            });
        }

        /// <summary>
        /// バージョン情報ログ出力処理.
        /// JPROのインストールバージョンをログ出力する.
        /// </summary>
        /// <param name="version">インストールバージョン.</param>
        public void OutputDeploymentResult(AppVersion version)
        {
            // 展開先のディレクトリ
            var backendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend", $"v{version}");
            var frontendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Frontend", $"v{version}");

            // インストールフォルダ内に存在する Jpro で開始するすべての dll を取得する
            var dllPath = Directory.GetFiles(backendPath)
                .Where(filePath =>
                {
                    var fileName = Path.GetFileName(filePath);
                    return fileName.StartsWith("Jpro") && fileName.EndsWith(".dll");
                })
                .ToList();

            // メッセージのベース
            var baseMessage = new StringBuilder();
            baseMessage.AppendLine($"JPROリリースバージョン\t{version}");
            OutputBackendDllVersion(baseMessage, "JproBackend", "バックエンド本体");
            OutputBackendDllVersion(baseMessage, "JproBackAB", "出納帳システム");
            OutputBackendDllVersion(baseMessage, "JproBackBR", "業務報告書システム");
            OutputBackendDllVersion(baseMessage, "JproBackIM", "在庫管理システム");
            OutputBackendDllVersion(baseMessage, "JproBackME", "薬袋システム");
            OutputBackendDllVersion(baseMessage, "JproBackMM", "店舗マスタメンテナンス");
            OutputBackendDllVersion(baseMessage, "JproBackPF", "印刷フレームワーク");
            OutputBackendDllVersion(baseMessage, "JproBackPS", "調剤システム");
            OutputBackendDllVersion(baseMessage, "JproBackPSReport", "調剤システム帳票定義");
            OutputBackendDllVersion(baseMessage, "JproBackRS", "レセプトシステム");
            OutputBackendDllVersion(baseMessage, "JproBackBizCommon", "業務共通機能");
            OutputBackendDllVersion(baseMessage, "JproBackSC", "共通機能(API)");
            OutputBackendDllVersion(baseMessage, "JproBackCommon", "共通機能(フレームワーク)");
            OutputBackendDllVersion(baseMessage, "JproBaseDbContext", "データベースアクセス");
            OutputFrontendVersion(baseMessage);

            // アプリログ上に同内容のログを記録する
            var logMessage = new StringBuilder();
            logMessage.AppendLine(string.Empty);
            logMessage.AppendLine($"--- インストール処理結果 START ---");
            logMessage.AppendLine(baseMessage.ToString());
            logMessage.AppendLine($"--- インストール処理結果 END ---");
            this.Logger.InformationLog(logMessage.ToString());

            // リリース結果ファイルを作成する
            var envUtil = new EnvironmentUtility(this.Configuration);
            (string pharmacyName, string pharmacyCd) = envUtil.GetPharmacyData();

            var resultFileContents = new StringBuilder();
            resultFileContents.AppendLine($"店舗コード\t{pharmacyCd}");
            resultFileContents.AppendLine($"店舗名\t{pharmacyName}");
            resultFileContents.AppendLine($"リリース日時\t{SystemDateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}");
            resultFileContents.AppendLine(baseMessage.ToString());

            if (!DirectoryAccessor.Exists(this.Configuration.Deployment.DeploymentResultDirectory))
            {
                DirectoryAccessor.CreateDirectory(this.Configuration.Deployment.DeploymentResultDirectory);
            }

            var resultFilePath = Path.Combine(
                this.Configuration.Deployment.DeploymentResultDirectory,
                $"JPRO_{pharmacyCd}_v{version.ToString("_")}_{pharmacyName}.txt");

            using var resultFile = new StreamWriter(resultFilePath, false, Encoding.GetEncoding("shift_jis"));
            resultFile.Write(resultFileContents.ToString());

            void OutputBackendDllVersion(StringBuilder message, string assembly, string name)
            {
                var targetPath = dllPath.FirstOrDefault(path =>
                    Path.GetFileName(path).Replace(".dll", string.Empty) == assembly);

                if (targetPath is null)
                {
                    message.AppendLine($"{name}\t{assembly}\t-");
                }
                else
                {
                    message.AppendLine($"{name}\t{assembly}\t{FileVersionInfo.GetVersionInfo(targetPath).FileVersion}");
                }
            }

            void OutputFrontendVersion(StringBuilder message)
            {
                var versionPath = Path.Combine(frontendPath, "assets", "version.json");
                var json = new JsonFile(versionPath);
                var versionNumber = json.GetValue<string>("version");
                message.AppendLine($"JPROフロントエンド\t-\t{versionNumber}");
            }
        }

        /// <summary>
        /// ファイルアクセスチェック処理.
        /// </summary>
        /// <param name="path">チェック対象のファイル.</param>
        /// <returns>true の場合、ファイルが存在してアクセス可能. false の場合、それ以外.</returns>
        private bool TestFile(string path)
        {
            try
            {
                // ファイルが存在しない場合、エラー
                if (!File.Exists(path))
                {
                    return false;
                }

                // ファイルがロックできるか確認
                using var stream = new FileStream(path, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 最新バージョンへのリダイレクト設定.
        /// VERSION: v0.0.0.0 形式でバージョンを設定する.
        /// PORT: ポート.
        /// </summary>
        private static readonly string RootRedirectNgixConfig =
@"location / {
  return 301 https://$host/<<VERSION>>$request_uri;
}
location /api/ {
  proxy_pass  http://localhost:<<PORT>>;
  proxy_set_header X-Forwarded-for $remote_addr;
  add_header X-Content-Type-Options ""nosniff"" always;
  add_header Strict-Transport-Security ""max-age=63072000; includeSubDomains; preload"" always;
  add_header Cache-Control ""no-store, no-cache, must-revalidate"" always;
}";

        /// <summary>
        /// 各バージョンのリダイレクト設定.
        /// VERSION: v0.0.0.0 形式でバージョンを設定する.
        /// PORT: ポート.
        /// LOCATION_INDEX: @index_0_0_0_0.
        /// VERSION_ROOT: バージョン番号を除いたパス.
        /// </summary>
        private static readonly string VesionNgixConfig =
@"location ~ ^/<<VERSION>>/api/.*$ {
  rewrite ^/<<VERSION>>/api/(.*)$ /api/$1 break;
  proxy_pass  http://localhost:<<PORT>>;
  proxy_set_header X-Forwarded-for $remote_addr;
  add_header X-Content-Type-Options ""nosniff"" always;
  add_header Strict-Transport-Security ""max-age=63072000; includeSubDomains; preload"" always;
  add_header Cache-Control ""no-store, no-cache, must-revalidate"" always;
}
location /<<VERSION>> {
  return 301 https://$host/<<VERSION>>/;
}
location /<<VERSION>>/ {
  alias <<VERSION_ROOT>>/<<VERSION>>/;
  try_files $uri $uri/ <<LOCATION_INDEX>>;
}
location <<LOCATION_INDEX>> {
    root <<VERSION_ROOT>>;
    try_files  /<<VERSION>>/index.html =404;
    add_header X-Content-Type-Options ""nosniff"" always;
    add_header X-XSS-Protection ""1; mode=block"";
    add_header X-Frame-Options ""DENY"";
    add_header Strict-Transport-Security ""max-age=63072000; includeSubDomains; preload"" always;
    add_header Content-Security-Policy ""default-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.gstatic.com; connect-src * data:; img-src * data:;worker-src 'self' blob:; object-src * data:; frame-src * data:;script-src-elem * data:;"";
    add_header Referrer-Policy ""no-referrer"" always;
    add_header Cache-Control ""no-store, no-cache, must-revalidate"" always;
}";
    }
}
