<Project Sdk="Microsoft.NET.Sdk.Worker">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Authors>日本調剤株式会社</Authors>
		<Version>0.0.0.7</Version>
		<RootNamespace>JproDeploymentService</RootNamespace>
		<Configurations>Debug;Release;DebugNoAR;ReleaseNoAR</Configurations>
		<ApplicationManifest>app.manifest</ApplicationManifest>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<AdditionalFiles Include="stylecop.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="ProcessX" Version="1.5.5" />
		<PackageReference Include="AutoMapper" Version="11.0.1" />
		<PackageReference Include="Kana.NET" Version="1.0.6" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="6.0.7" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Serilog.Extensions.Hosting" Version="4.2.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.0.0" />
		<PackageReference Include="Serilog.Enrichers.AssemblyName" Version="1.0.9" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
		<PackageReference Include="Serilog.Enrichers.Memory" Version="1.0.4" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
		<PackageReference Include="Serilog.Exceptions" Version="8.3.0" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="4.0.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="CommandLineParser" Version="2.9.1" />
		<PackageReference Include="SharpZipLib" Version="1.4.2" />
	</ItemGroup>

	<ItemGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<Reference Include="JproBackCommon">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\Debug\net6.0\JproBackCommon.dll</HintPath>
		</Reference>
		<Reference Include="JproBaseDbContext">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\Debug\net6.0\JproBaseDbContext.dll</HintPath>
		</Reference>
		<Reference Include="nddDec01_Net">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\Debug\net6.0\nddDec01_Net.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<Reference Include="JproBackCommon">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\Release\net6.0\JproBackCommon.dll</HintPath>
		</Reference>
		<Reference Include="JproBaseDbContext">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\Release\net6.0\JproBaseDbContext.dll</HintPath>
		</Reference>
		<Reference Include="nddDec01_Net">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\Release\net6.0\nddDec01_Net.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)|$(Platform)'=='DebugNoAR|AnyCPU'">
		<Reference Include="JproBackCommon">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\DebugNoAR\net6.0\JproBackCommon.dll</HintPath>
		</Reference>
		<Reference Include="JproBaseDbContext">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\DebugNoAR\net6.0\JproBaseDbContext.dll</HintPath>
		</Reference>
		<Reference Include="nddDec01_Net">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\DebugNoAR\net6.0\nddDec01_Net.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseNoAR|AnyCPU'">
		<Reference Include="JproBackCommon">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\ReleaseNoAR\net6.0\JproBackCommon.dll</HintPath>
		</Reference>
		<Reference Include="JproBaseDbContext">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\ReleaseNoAR\net6.0\JproBaseDbContext.dll</HintPath>
		</Reference>
		<Reference Include="nddDec01_Net">
			<HintPath>..\..\..\JproBackendRepo\JproBackend\JproBackCommon\bin\ReleaseNoAR\net6.0\nddDec01_Net.dll</HintPath>
		</Reference>
	</ItemGroup>
</Project>
