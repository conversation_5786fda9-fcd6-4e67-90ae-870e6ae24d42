﻿// <copyright file="FileWatcher.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Channels;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproDeploymentService.Common.Util;

    /// <summary>
    /// ファイル変更監視クラス.
    /// フォルダの変更点を監視する機能.
    /// </summary>
    internal sealed class FileWatcher : IDisposable
    {
        /// <summary>
        /// <see cref="FileWatcher"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="interval">デバウンスの待機期間(ミリ秒).</param>
        /// <param name="logger">ロガー.</param>
        public FileWatcher(int interval, FileLogger<FileWatcher> logger)
        {
            this.Logger = logger;

            // デバウンス処理
            this.DebounceManager = new ()
            {
                Interval = interval,
            };

            // イベントキュー
            this.FileChangeActionQueue = Channel.CreateUnbounded<FileChangeEventArg>(new UnboundedChannelOptions
            {
                SingleReader = true,
            });
        }

        /// <summary>
        /// フォルダが更新されたときのイベントハンドラを取得または設定する.
        /// </summary>
        public Func<FileChangeEventArg, string> FileChangeAction { get; set; } = null;

        /// <summary>
        /// ファイル監視処理.
        /// 指定したディレクトリを監視する.
        /// </summary>
        /// <param name="directory">ディレクトリの完全パス.</param>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>読み取りされたファイルパス情報.</returns>
        public Task<string> ObserveDirectory(string directory, CancellationToken cancellationToken)
        {
            if (!DirectoryAccessor.Exists(directory))
            {
                throw new ArgumentException($"{nameof(directory)} 存在しないディレクトリです。");
            }

            // ファイル監視機能を初期化
            this.Watcher = new FileSystemWatcher(directory);
            this.Watcher.NotifyFilter =
                NotifyFilters.CreationTime |
                NotifyFilters.DirectoryName |
                NotifyFilters.FileName |
                NotifyFilters.LastAccess |
                NotifyFilters.LastWrite |
                NotifyFilters.Size;

            this.Watcher.IncludeSubdirectories = true;
            this.Watcher.Changed += this.HandleWatcherChanged;
            this.Watcher.Created += this.HandleWatcherChanged;
            this.Watcher.Deleted += this.HandleWatcherChanged;
            this.Watcher.Renamed += this.HandleWatcherChanged;
            this.Watcher.Error += this.HandleWatcherError;

            // ファイル監視機能を起動
            this.Logger.DebugLog($"{directory} を監視します。");
            this.Watcher.EnableRaisingEvents = true;

            // ファイル監視機能の通知を待ち受けして、イベントを発火する処理を実行する
            this.CancellationToken = cancellationToken;

            // 待機ループを実行する
            return this.ExecuteMainLoop();
        }

        /// <summary>
        /// リソース解放機能処理.
        /// ファイル監視機能を解放する.
        /// </summary>
        public void Dispose()
        {
            // 内部ループ向けに停止フラグを有効化する
            this.IsDisposing = true;

            // null を設定すると外部へのイベントが発生しなくなる
            this.FileChangeAction = null;

            if (this.Watcher is not null)
            {
                this.Watcher.EnableRaisingEvents = false;
                this.Watcher.Changed -= this.HandleWatcherChanged;
                this.Watcher.Created -= this.HandleWatcherChanged;
                this.Watcher.Deleted -= this.HandleWatcherChanged;
                this.Watcher.Renamed -= this.HandleWatcherChanged;
                this.Watcher.Error -= this.HandleWatcherError;
                this.Watcher.Dispose();
            }
        }

        /// <summary>
        /// 監視イベント強制発生処理.
        /// プログラム設計の都合上、監視処理開始時に強制的にファイル更新のイベントを実行する.
        /// </summary>
        /// <param name="directory">参照ディレクトリ.</param>
        public void ReigsterEventQueue(string directory)
        {
            lock (s_parameterLock)
            {
                this.FileWatcherParameters.AddRange(
                    DirectoryAccessor.GetFiles(directory).Select(file => new FileWatcherParameter
                    {
                        ChangeTypes = WatcherChangeTypes.Created,
                        Path = file,
                    }));
            }

            this.InvokeWatcherAction();
        }

        /// <summary>
        /// ファイル変更検知イベント処理.
        /// 監視処理でファイルシステムが更新されたときのイベント.
        /// </summary>
        /// <param name="sender">イベントの発生源.</param>
        /// <param name="e">ファイルシステムイベントのパラメータ.</param>
        private void HandleWatcherChanged(object sender, FileSystemEventArgs e)
        {
            lock (s_parameterLock)
            {
                this.FileWatcherParameters.Add(new FileWatcherParameter
                {
                    ChangeTypes = e.ChangeType,
                    Path = e.FullPath,
                });
            }

            this.InvokeWatcherAction();
        }

        /// <summary>
        /// ファイル変更検知エラーイベント処理.
        /// 監視処理でエラーが発生したときのイベントハンドラを実行するためにキュー登録する.
        /// </summary>
        /// <param name="sender">イベントの発生源.</param>
        /// <param name="e">ファイルシステムイベントのパラメータ.</param>
        private void HandleWatcherError(object sender, ErrorEventArgs e)
        {
            lock (s_parameterLock)
            {
                this.FileWatcherParameters.Add(new FileWatcherParameter
                {
                    Exception = e.GetException(),
                });
            }

            this.InvokeWatcherAction();
        }

        /// <summary>
        /// イベントキュー登録処理.
        /// フォルダが更新されたときのイベントハンドラを実行するためにキュー登録する.
        /// </summary>
        private void InvokeWatcherAction()
        {
            // イベントがアサインされていない場合、キュー追加しない
            if (this.FileChangeAction is null)
            {
                return;
            }

            this.DebounceManager.Debounce(
                () =>
                {
                    List<FileWatcherParameter> parameters;
                    lock (s_parameterLock)
                    {
                        parameters = this.FileWatcherParameters.ToList();
                        this.FileWatcherParameters = new ();
                    }

                    this.FileChangeActionQueue.Writer.WriteAsync(new FileChangeEventArg
                    {
                        Id = Guid.NewGuid().ToString().Replace("-", string.Empty),
                        EventTime = DateTime.Now,
                        Parameters = parameters,
                        CancellationToken = this.CancellationToken,
                    });
                }, this.CancellationToken);
        }

        /// <summary>
        /// メインループ処理.
        /// キューを監視して、フォルダが更新されたときのイベントハンドラを実行する.
        /// </summary>
        /// <returns>読み取りされたファイル情報.</returns>
        private async Task<string> ExecuteMainLoop()
        {
            this.Logger.DebugLog($"フォルダ監視処理のイベントキューを開始します。");

            while (await this.FileChangeActionQueue.Reader.WaitToReadAsync(this.CancellationToken) &&
                !this.CancellationToken.IsCancellationRequested &&
                !this.IsDisposing)
            {
                this.Logger.DebugLog($"ディレクトリ更新のイベント処理を実行します。");

                // キューを読み出す
                var eventArgs = await this.FileChangeActionQueue
                    .Reader
                    .ReadAsync(this.CancellationToken);

                // ファイル情報をデバッグ出力
                var identity = $"(識別子 {eventArgs.Id} 発生時刻 {eventArgs.EventTime.ToString("yyyy/MM/dd HH:mm:ss")})";
                eventArgs.Parameters.ForEach(parameter =>
                {
                    if (parameter.HasError)
                    {
                        this.Logger.ErrorLog(parameter.Exception, $"フォルダの監視処理でエラーが発生しました。{identity}");
                    }
                    else
                    {
                        this.Logger.DebugLog($"変更情報 パス:{parameter.Path} 変更内容:{parameter.ChangeTypes} {identity}");
                    }
                });

                // 同期でアクションを実行する
                if (this.FileChangeAction?.Invoke(eventArgs) is string path)
                {
                    return path;
                }

                this.Logger.DebugLog($"イベント処理が完了しました。次のディレクトリ更新まで再度待機します。");
            }

            this.Logger.DebugLog($"フォルダ監視処理のイベントキューを終了します。");
            return null;
        }

        /// <summary>
        /// キャンセルトークン.
        /// </summary>
        private CancellationToken CancellationToken { get; set; }

        /// <summary>
        /// オブジェクトが破棄済みかどうか判別するためのフラグ.
        /// true の場合、破棄済みである. false の場合、破棄済みでない.
        /// </summary>
        private bool IsDisposing { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<FileWatcher> Logger { get; }

        /// <summary>
        /// デバウンス処理を取得する.
        /// </summary>
        private DebounceManager DebounceManager { get; }

        /// <summary>
        /// ファイル監視機能を取得または設定する.
        /// </summary>
        private FileSystemWatcher Watcher { get; set; }

        /// <summary>
        /// ワーカーの管理クラスを取得する.
        /// </summary>
        private Channel<FileChangeEventArg> FileChangeActionQueue { get; set; }

        /// <summary>
        /// パラメーターを取得または設定する.
        /// </summary>
        private List<FileWatcherParameter> FileWatcherParameters { get; set; } = new ();

        /// <summary>
        /// パラメータ―のロックオブジェクト.
        /// </summary>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("StyleCop.CSharp.NamingRules", "SA1311:Static readonly fields should begin with upper-case letter", Justification = "コーディング規約に従う")]
        private static readonly object s_parameterLock = new ();
    }

    /// <summary>
    /// 単一のファイル処理イベントの引数.
    /// </summary>
    internal class FileChangeEventArg
    {
        /// <summary>
        /// イベント日時を取得または設定する.
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// イベントIDを取得または設定する.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// ファイル監視機能のイベント情報を取得または設定する.
        /// </summary>
        public List<FileWatcherParameter> Parameters { get; set; }

        /// <summary>
        /// キャンセルトークンを取得または設定する.
        /// </summary>
        public CancellationToken CancellationToken { get; set; }
    }

    /// <summary>
    /// ファイル監視機能のイベント情報.
    /// </summary>
    internal class FileWatcherParameter
    {
        /// <summary>
        /// 変更種類を取得または設定する.
        /// </summary>
        public WatcherChangeTypes? ChangeTypes { get; set; }

        /// <summary>
        /// 変更点のあったパスを取得または設定する.
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 例外データを取得または設定する.
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// エラーが発生したか判別するためのフラグを取得または設定する.
        /// true の場合、エラーが発生した. false の場合、エラーが発生してない.
        /// </summary>
        public bool HasError => this.Exception is not null;
    }
}
