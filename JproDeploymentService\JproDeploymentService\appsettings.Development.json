{"JproRelease": {"Database": {"ChozaiDbConnectionString": "Data Source=localhost;Initial Catalog=chozaidb;User ID=830b9b3880d01b53e27e78bc8d6fa8c230bf978947d066e79db305d702c7e8ba6fde618f59572ec68f8857a4c7fa1610;Password=************************************************************************************************;Connect Timeout=3000;"}, "Deployment": {"SharedDirectory": "D:\\EXESHARE\\", "JproBaseDirectory": "D:\\system\\JPRO\\", "TempDirectory": "D:\\system\\JPRO\\Work\\", "DeploymentResultDirectory": "D:\\system\\JPRO\\JproDeployment\\result", "NginxDirectory": "D:\\system\\JPRO\\nginx\\", "ErrorRetryInterval": 30}, "Etc": {"DbDebounceInterval": 10, "FileDebounceInterval": 10, "FileRetryCount": 5, "FileRetryInitialInterval": 5, "FileRetryMaxInterval": 60}}, "Serilog": {"Using": ["SeriLog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Async"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Debug", "System": "Debug"}}, "WriteTo": [{"Name": "File", "Args": {"path": "Logs/log.txt", "outputTemplate": "| {Timestamp:HH:mm:ss.fff} | {Level:u4} | {ProcessId:00} | {ThreadId:00} | {MessageId} | {Message:j} | {SourceContext} | {MemberName} | {LineNumber} | {MachineName} | {EnvironmentUserName} | {NewLine}{Exception}", "rollingInterval": "Day"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "| {Timestamp:HH:mm:ss.fff} | {Level:u4} | {ProcessId:00} | {ThreadId:00} | {MessageId} | {Message:j} | {SourceContext} | {MemberName} | {LineNumber} | {MachineName} | {EnvironmentUserName} | {NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithThreadId", "WithProcessId"]}}