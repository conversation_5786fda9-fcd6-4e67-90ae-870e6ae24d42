﻿// <copyright file="UninstallTask.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.Common.Util;
    using JproDeploymentService.Service.Operation;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// アンインストールタスククラス.
    /// 1回分のアンインストール作業のラウンドトリップを制御するための管理クラス.
    /// </summary>
    internal class UninstallTask
    {
        /// <summary>
        /// <see cref="UninstallTask"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="loggerFactory">ロガーファクトリ.</param>
        /// <param name="environment">環境データ.</param>
        /// <param name="messageProvider">メッセージ情報取得処理.</param>
        public UninstallTask(
            ILoggerFactory loggerFactory,
            IHostEnvironment environment,
            IMessageProvider messageProvider)
        {
            this.LoggerFactory = loggerFactory;
            this.Logger = this.SupplyLogger<UninstallTask>();

            this.Configuration = InternalActivator.CreateConfiguration(environment.EnvironmentName, false)
                .ToJproReleaseConfiguration();
            this.MessageProvider = messageProvider;
        }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private ILoggerFactory LoggerFactory { get; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<UninstallTask> Logger { get; }

        /// <summary>
        /// メッセージ取得クラスを取得する.
        /// </summary>
        private IMessageProvider MessageProvider { get; }

        /// <summary>
        /// アンインストール処理.
        /// 1回分のアンインストール作業を実行する.
        /// 最新バージョン以外のサービスを削除する.
        /// </summary>
        /// <param name="cancelToken">インストール処理を停止するためのトークン.</param>
        /// <param name="uninstallImmediately">
        /// 直ぐに古いバージョンを使用不可にする.
        /// true にすると即時、アンインストール作業を行う.
        /// false にすると、以降猶予期間待機後、アンインストール作業を行う.
        /// </param>
        /// <returns>インストールされたバージョン.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public async Task Execute(CancellationToken cancelToken, bool uninstallImmediately)
        {
            // 環境情報取得処理を作成
            var environmentUtility = this.CreateEnvironmentUtility();

            // SYS_SHOP_INI を即時更新する
            environmentUtility.RefreshIniTableCache();

            // インストールバージョンを取得する
            if (environmentUtility.GetJproServiceVersion() is not AppVersion installVersion)
            {
                // インストールされているバージョンがなければ終了
                return;
            }

            // アンインストール対象のデータを取得する
            List<(BackendService Servive, AppVersion Version)> uninstallVersions = GetUninstallVersions();

            // 削除対象のサービスがなければ終了
            if (!uninstallVersions.Any())
            {
                return;
            }

            this.Logger.InformationLog("アンインストール処理を開始します。");
            uninstallVersions.ForEach(version => this.Logger.InformationLog($"アンインストール対象のバージョン: {version.Version}"));

            // サービス操作機能処理を作成
            var commandLineOperation = this.CreateCommandLineOperation();

            // ファイル処理機能を生成する
            var fileOperation = this.CreateFileOperation();

            // 待機モードの場合、一定期間待機する
            if (!uninstallImmediately)
            {
                // 待機時間を取得
                var delayTime = DbStaticCache.SysShopIni.GetCurrentSettings()
                    .First(ini => ini.DataDiv == ReleaseConstants.DataDivUnistallDelayTime)
                    .IntValue ?? (60 * 30);

                // 停止予定時刻
                var terminateTime = DateTime.Now.AddSeconds(delayTime);
                uninstallVersions.ForEach(async service =>
                    await fileOperation.CreateTerminateNotifyFile(installVersion, service.Version, terminateTime));

                this.Logger.InformationLog("アンインストール対象のバックエンドサービスに停止通知を行いました。");

                this.Logger.InformationLog($"アンインストールまで {delayTime} 秒間待機します。");
                await Task.Delay(delayTime * 1000, cancelToken);
                this.Logger.InformationLog("アンインストール処理を再開します。");

                // 待機中にインストールの状況が変更されている可能性があるので、サービス情報を再取得する
                uninstallVersions = GetUninstallVersions();

                // 削除対象のサービスがなければ終了
                if (!uninstallVersions.Any())
                {
                    this.Logger.InformationLog("アンインストール対象が存在しないため終了します。");
                    return;
                }
            }

            // サービスを停止する
            uninstallVersions.ForEach(service => commandLineOperation.StopJproBackend(service.Version));
            this.Logger.InformationLog("アンインストール対象のバックエンドサービスを停止しました。");

            // インストール先を削除する
            uninstallVersions.ForEach(service => fileOperation.DeleteInstallPath(service.Version));
            this.Logger.InformationLog("アンインストール対象のファイルを削除しました。");

            // NGINX の設定ファイルは次の更新まで削除しない (削除されたAPIエラー処理を行うため)
            this.Logger.InformationLog($"JPROのアンインストールに成功しました。");

            // 削除対象のサービス一覧を取得する
            List<(BackendService Servive, AppVersion Version)> GetUninstallVersions()
            {
                // 即時停止モード(uninstallImmediately == true)の場合、起動中のサービスは停止しない
                return environmentUtility.GetJproBackendServices()
                    .Where(service =>
                        service.Version < installVersion &&
                        !(uninstallImmediately && service.Servive.Status))
                    .ToList();
            }
        }

        /// <summary>
        /// ファイル処理操作クラス生成処理.
        /// </summary>
        /// <returns>ファイル操作機能.</returns>
        private FileOperation CreateFileOperation()
        {
            return new FileOperation(
                this.SupplyLogger<FileOperation>(),
                this.Configuration);
        }

        /// <summary>
        /// Windowsサービス操作クラス生成処理.
        /// </summary>
        /// <returns>Windows サービス操作機能.</returns>
        private CommandLineOperation CreateCommandLineOperation()
        {
            return new CommandLineOperation(
                this.SupplyLogger<CommandLineOperation>(),
                this.Configuration);
        }

        /// <summary>
        /// 環境情報ユーティリティクラス生成処理.
        /// </summary>
        /// <returns>ファイル操作機能.</returns>
        private EnvironmentUtility CreateEnvironmentUtility()
        {
            // 現在のインストールバージョンを確認
            return new EnvironmentUtility(
                this.Configuration,
                this.SupplyLogger<EnvironmentUtility>());
        }

        /// <summary>
        /// ロガー生成処理.
        /// </summary>
        /// <typeparam name="TCategory">ロガーのカテゴリクラス.</typeparam>
        /// <returns>ファイルロガー.</returns>
        private FileLogger<TCategory> SupplyLogger<TCategory>()
        {
            return FileLogger<TCategory>.CreateFileLogger(
                this.LoggerFactory.CreateLogger<TCategory>(),
                this.MessageProvider);
        }
    }
}
