{"$schema": "https://raw.githubusercontent.com/DotNetAnalyzers/StyleCopAnalyzers/master/StyleCop.Analyzers/StyleCop.Analyzers/Settings/stylecop.schema.json", "settings": {"documentationRules": {"companyName": "日本調剤", "copyrightText": "Copyright (c) {companyName}. All rights reserved.", "xmlHeader": true, "documentExposedElements": true, "documentPrivateElements": true, "documentPrivateFields": true}, "layoutRules": {"newlineAtEndOfFile": "require"}, "indentation": {"indentationSize": 4, "useTabs": false}, "namingRules": {"allowedHungarianPrefixes": ["db", "ip"]}, "orderingRules": {"systemUsingDirectivesFirst": true, "usingDirectivesPlacement": "insideNamespace", "elementOrder": ["kind", "constant", "accessibility", "static", "readonly"]}}}