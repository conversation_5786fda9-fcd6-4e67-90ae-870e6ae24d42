﻿// <copyright file="LifetimeEventService.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Service
{
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Reflection;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Bootstrap;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;
    using static JproBackend.JproBackCommon.Common.Base.Service.LoggerAuditTypes;

    /// <summary>
    /// ホストサービスライフサイクル管理クラス.
    /// .NET ホストのライフタイムイベント (アプリ開始・終了時) に対応して JPRO アプリとして必要な処理を行う.
    /// </summary>
    internal class LifetimeEventService : IHostedService
    {
        /// <summary>
        /// <see cref="JwtBearerConfigureService"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="logger">ロガー.</param>
        /// <param name="fileLoggerOfAuditLogger">監査ロガーが内部で使用するロガーを取得する.</param>
        /// <param name="appLifetime">アプリのライフタイムイベント.</param>
        /// <param name="cache">アプリキャッシュ.</param>
        /// <param name="environment">ホスト環境情報.</param>
        /// <param name="messageProvider">メッセージ取得処理.</param>
        public LifetimeEventService(
            ILogger<LifetimeEventService> logger,
            IHostApplicationLifetime appLifetime,
            IWebHostEnvironment environment,
            IMessageProvider messageProvider)
        {
            // ファイルロガーの構築
            this.Logger = FileLogger<LifetimeEventService>.CreateFileLogger(logger, messageProvider);

            this.AppLifetime = appLifetime;
            this.Environment = environment;
        }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<LifetimeEventService> Logger { get; }

        /// <summary>
        /// アプリのライフタイムイベントを取得する.
        /// </summary>
        private IHostApplicationLifetime AppLifetime { get; }

        /// <summary>
        /// ホスト環境情報を取得する.
        /// </summary>
        private IWebHostEnvironment Environment { get; }

        /// <summary>
        /// メッセージ取得処理.
        /// </summary>
        [SuppressMessage("Major Code Smell", "S1144:Unused private types or members should be removed", Justification = "将来の機能拡張に備えて実装済み状態")]
        private IMessageProvider MessageProvider { get; }

        /// <summary>
        /// .NET ホストの起動時にこのサービスの初期化処理を行う.
        /// </summary>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>この非同期処理は戻り値を持たない.</returns>
        public Task StartAsync(CancellationToken cancellationToken)
        {
            // アプリ開始時のイベント
            this.AppLifetime.ApplicationStarted.Register(this.OnStarted);

            // アプリ終了時のイベント
            this.AppLifetime.ApplicationStopped.Register(this.OnApplicationStopped);

            // アプリ終了中のイベント
            this.AppLifetime.ApplicationStopping.Register(this.OnApplicationStopping);

            return Task.CompletedTask;
        }

        /// <summary>
        /// サービス開始処理.
        /// .NET ホストのシャットダウン時にシステムを安全に停止するための処理を実施する.
        /// </summary>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>この非同期処理は戻り値を持たない.</returns>
        public Task StopAsync(CancellationToken cancellationToken)
        {
            this.Logger.InformationLog("JPRO リリース機能が終了指示を受けました。");

            return Task.CompletedTask;
        }

        /// <summary>
        /// アプリケーション開始イベントハンドラ.
        /// アプリ開始時のイベント.
        /// </summary>
        private void OnStarted()
        {
            // 文字コードにSJISを追加
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            this.Logger.InformationLog("JPRO リリース機能のサービスを開始しました。");
        }

        /// <summary>
        /// アプリケーションシャットダウン中イベントハンドラ.
        /// アプリ終了中のイベント.
        /// </summary>
        private void OnApplicationStopping()
        {
            this.Logger.InformationLog("JPRO リリース機能のサービスが終了中です。");
        }

        /// <summary>
        /// アプリケーションシャットダウンイベントハンドラ.
        /// アプリ終了時のイベント.
        /// </summary>
        private void OnApplicationStopped()
        {
            this.Logger.InformationLog("JPRO リリース機能のサービスが終了しました。");
        }
    }
}
