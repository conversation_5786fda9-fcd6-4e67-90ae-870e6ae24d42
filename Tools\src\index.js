const _ = require("lodash");
const minimist = require("minimist");
const chalk = require("chalk");
const fs = require("fs");
const fsExtra = require("fs-extra");
const path = require('path');
const crypto = require("crypto");
const childProcess = require('child_process');
const archiver = require('archiver');

/**
 * 正しく終了しました。
 */
 const RETURN_CODE_SUCCESS = "0";
 /**
  * パラメータが不正規です。
  */
 const RETURN_CODE_INVALID_ARGMENT = "1";
 /**
  * 作業フォルダの生成に失敗しました。
  */
   const RETURN_CODE_ERROR_WORK_FORLDER = "2";
/**
  * コマンド実行が発生しました。
  */
      const RETURN_CODE_COMMAND_ERROR = "3";
 /**
  * システムエラーが発生しました。
  */
   const RETURN_CODE_SYSTEM_ERROR = "9";

 /** 
 * 終了コード
 */
let returnCode = RETURN_CODE_SUCCESS;

 /** 
 * 作業フォルダ
 */
let workFolder = undefined;


/**
 * コマンドライン引数の妥当性を検証する
 */
function testArgs(args) {
  if (!_.has(args, "_") || 
    !_.isArray(args._) || 
    args._.length != 1 ||
    (args._[0] !== "jpro" && args._[0] !== "jproDeployment")) {
    console.log(chalk.red("モードを指定してください"));
    console.log(chalk.yellow("jpro") + ": JPROのインストール資材作成(JproRelease.zip)処理");
    console.log(chalk.yellow("jproDeployment") + ": JPROリリース機能のインストール資材(jproDeploymentService.zip)作成処理");
    return false;
  }

  const mode = args._[0];

  // jpro --branch /develop/xxxx --workfolder "c:\yyy\yyy"
  // jpro --frontend /develop/xxxx --backend /develop/yyyy --version nnnn --workfolder "c:\zzz\zzz"
  if (mode === "jpro") {
    if (!_.isEmpty(args.branch) && !_.isEmpty(args.workfolder)) {
      // noop
    } else if (!_.isEmpty(args.frontend) && !_.isEmpty(args.backend) && !_.isEmpty(args.version) && !_.isEmpty(args.workfolder)) {
      // noop
    } else {
      console.log(chalk.red("jpro モードの場合以下のパラメータを指定してください"));
      console.log(chalk.yellow("JproReleaseモードの場合") + ": --branch JproReleaseのブランチ --workfolder 出力先ワークフォルダ");
      console.log(chalk.yellow("直接指定モードの場合") + ": --frontend jpro-frontend-repoのブランチ --backend JproBackendRepoのブランチ --version バージョン --workfolder 出力先ワークフォルダ");
      return false;
    }
  }

  // jproDeployment --service /develop/xxxx --backend /develop/yyy --workfolder "c:\zzz\zzz"
  if (mode === "jproDeployment") {
    if (!_.isEmpty(args.service) && !_.isEmpty(args.backend) && !_.isEmpty(args.workfolder)) {
      // noop
    } else {
      console.log(chalk.red("jproDeployment モードの場合以下のパラメータを指定してください"));
      console.log(chalk.yellow("直接指定モードの場合") + ": --service JproDeplyomentServiceのブランチ --backend JproBackendRepoのブランチ --workfolder 出力先ワークフォルダ");
      return false;
    }
  }

  // workfolder の存在チェック
  const parent = path.dirname(args.workfolder);
  if (!parent || !fs.existsSync(parent)) {
    console.log(chalk.red(`パラメータ --workfolder ${parent} の出力先フォルダが存在しません。` ));
    return false;
  }

  if (fs.existsSync(args.workfolder)) {
    console.log(chalk.red(`パラメータ --workfolder ${parent} の出力先パスは既に存在します。` ));
    return false;
  }

  return true;
}

/**
 * 作業フォルダを削除する
 */
function createWorkFolder() {
  fs.mkdirSync(path.dirname(workFolder));
  fs.mkdirSync(workFolder);
  if (!fs.existsSync(workFolder)) {
    console.log("作業フォルダの生成に失敗しました");
    return false;
  }

  return true;
}

/**
 * 作業フォルダを生成する
 */
 function deleteWorkFolder() {
  if (workFolder && fs.existsSync(workFolder)) {
    fsExtra.removeSync(workFolder);
  }
}

/**
 * 出力ファイル先をzip生成フォルダへ複製する
 */
function moveJproReleaseOutputFiles() {
  fsExtra.mkdirSync(path.join(workFolder, ".\\JproRelease"));

  fsExtra.copySync(
    path.join(workFolder, ".\\jpro-frontend-repo\\jpro-frontend\\dist\\frontendWork"),
    path.join(workFolder, ".\\JproRelease\\JproFrontend")
  );

  fsExtra.copySync(
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend\\JproBackController\\bin\\Release\\net6.0"),
    path.join(workFolder, ".\\JproRelease\\JproBackend")
  );
}

/**
 * バージョン定義を出力する
 */
function exportReleaseVersionFile(version) {
  const contents =
    "{\r\n" +
    "    \"Version\": \"" + version + "\"\r\n" +
    "}\r\n";

  fs.writeFileSync(path.join(workFolder, ".\\JproRelease\\JproRelease.json"), contents);
}

/**
 * zipファイルを生成する
 */
async function archiveJproReleaseZip() {
  return new Promise((resolve) => {
    const archive = archiver.create('zip', {
      zlib: { level: 9 }
    });
    const output = fs.createWriteStream(path.join(path.dirname(workFolder), ".\\JproRelease.zip"));
    output.on("close", function () {
      resolve();
    });
    archive.pipe(output);
    archive.glob("*", { cwd: path.join(workFolder, ".\\JproRelease\\") });
    archive.glob("JproBackend/**/*", { cwd: path.join(workFolder, ".\\JproRelease\\") });
    archive.glob("JproFrontend/**/*", { cwd: path.join(workFolder, ".\\JproRelease\\") });
    archive.finalize();
  });
}

/**
 * 出力ファイル先をzip生成フォルダへ複製する
 */
 function moveJproDeploymentServiceOutputFiles() {
  fsExtra.mkdirSync(path.join(workFolder, ".\\JproDeploymentServiceWork"));

  fsExtra.copySync(
    path.join(workFolder, ".\\JproDeploymentService\\JproDeploymentService\\JproDeploymentService\\bin\\Release\\net6.0"),
    path.join(workFolder, ".\\JproDeploymentServiceWork")
  );
}

/**
 * zipファイルを生成する
 */
 async function archiveJproDeploymentServiceZip() {
  return new Promise((resolve) => {
    const archive = archiver.create('zip', {
      zlib: { level: 9 }
    });
    const output = fs.createWriteStream(path.join(path.dirname(workFolder), ".\\JproDeploymentService.zip"));
    output.on("close", function () {
      resolve();
    });
    archive.pipe(output);
    archive.glob("**/*", { cwd: path.join(workFolder, ".\\JproDeploymentServiceWork\\") });
    archive.finalize();
  });
}

/**
 * コマンドを実行する
 */
async function executProcess(command, args, workDir) {
  return new Promise((resolve)=> {
    const proc = childProcess.spawn(command, args, { cwd: workDir, stdio: 'inherit' });
    proc.on('exit', (code)=>{
      resolve(code);
    });
  });
}

/**
 * jpro (直接指定モード) モード
 */
async function executeJproDirectMode(args) {
  if (!createWorkFolder()) {
    return RETURN_CODE_ERROR_WORK_FORLDER;
  }

  console.time();

  console.log(`[1] jpro-frontend-repo レポジトリ (${args.frontend} ブランチ) をチェックアウトします。`);
  console.time("[1]");
  // git clone --depth 1 -b develop/config/main-sst1 https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/jpro-frontend-repo
  let returnCode = await executProcess(
    "git",
    ["clone", "--depth", "1", "-b", args.frontend, "https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/jpro-frontend-repo"],
    workFolder
  );
  console.timeEnd("[1]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[2] jpro-frontend-repo レポジトリ (${args.frontend} ブランチ) のサブモジュールをチェックアウトします。`);
  console.time("[2]");
  // git submodule update --init --recursive --recommend-shallow --depth 1`
  returnCode = await executProcess(
    "git",
    ["submodule", "update", "--init", "--recursive", "--recommend-shallow", "--depth", "1"],    
    path.join(workFolder, ".\\jpro-frontend-repo\\jpro-frontend")
  );
  console.timeEnd("[2]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[3] npmパッケージをインストールします。`);
  console.time("[3]");
  // npm.cmd
  returnCode = await executProcess(
    "npm.cmd",
    ["ci"],    
    path.join(workFolder, ".\\jpro-frontend-repo\\jpro-frontend")
  );
  console.timeEnd("[3]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[4] フロントエンドビルドを実行します。`);
  console.time("[4]");
  fs.copyFileSync(
    path.join(__dirname, "jpro-frontend-build.bat"),
    path.join(workFolder, ".\\jpro-frontend-repo\\jpro-frontend", "jpro-frontend-build.bat"),
  );
  // set NODE_OPTIONS=--max-old-space-size=8192
  // ng build --configuration=production
  returnCode = await executProcess(
    "jpro-frontend-build.bat",
    [],    
    path.join(workFolder, ".\\jpro-frontend-repo\\jpro-frontend")
  );
  console.timeEnd("[4]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[5] JproBackendRepo レポジトリ (${args.backend} ブランチ) をチェックアウトします。`);
  console.time("[5]");
  // git clone --depth 1 -b release/*******/main-sst2/20231012 https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/JproBackendRepo
  returnCode = await executProcess(
    "git",
    ["clone", "--depth", "1", "-b", args.backend, "https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/JproBackendRepo"],
    workFolder
  );
  console.timeEnd("[5]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[6] JproBackendRepo レポジトリ (${args.backend} ブランチ) のサブモジュールをチェックアウトします。`);
  console.time("[6]");
  // git submodule update --init --recursive --recommend-shallow --depth 1
  returnCode = await executProcess(
    "git",
    ["submodule", "update", "--init", "--recursive", "--recommend-shallow", "--depth", "1"],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend")
  );
  console.timeEnd("[6]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[7] dotnet パッケージをインストールします。`);
  console.time("[7]");
  // dotnet restore
  returnCode = await executProcess(
    "dotnet",
    ["restore"],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend")
  );
  console.timeEnd("[7]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[8] .NET Framework パッケージをインストールします。`);
  console.time("[8]");
  fs.copyFileSync(
    path.join(__dirname, "nuget.exe"),
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend", "nuget.exe"),
  );
  fs.copyFileSync(
    path.join(__dirname, "dotnet-framework-restore.bat"),
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend", "dotnet-framework-restore.bat"),
  );
  // call "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
  // nuget.exe restore
  // MSBuild JproBackend.sln -property:Configuration=Release /t:"JproBackPSReport:clean;rebuild"
  returnCode = await executProcess(
    "dotnet-framework-restore.bat",
    [],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend")
  );
  console.timeEnd("[8]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[9] バックエンドビルドを実行します。`);
  console.time("[9]");
  // dotnet build --configuration Release
  returnCode = await executProcess(
    "dotnet",
    ["build", "--configuration", "Release"],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend\\JproBackController")
  );
  console.timeEnd("[9]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[10] ビルド結果を抽出します。`);
  console.time("[10]");
  moveJproReleaseOutputFiles();
  console.timeEnd("[10]");

  console.log(`[11] バージョン定義ファイルを出力します。`);
  console.time("[11]");
  exportReleaseVersionFile(args.version);
  console.timeEnd("[11]");

  console.log(`[12] リリース資材を生成します。`);
  console.time("[12]");
  await archiveJproReleaseZip();
  console.timeEnd("[12]");

  console.log(`リリース資材の生成が完了しました。`);
  console.timeEnd();

  return RETURN_CODE_SUCCESS;
}

/**
 * jproDeployment モード
 */
async function executeJproDeploymentMode(args) {
  if (!createWorkFolder()) {
    return RETURN_CODE_ERROR_WORK_FORLDER;
  }

  console.time();

  console.log(`[1] JproBackendRepo レポジトリ (${args.backend} ブランチ) をチェックアウトします。`);
  console.time("[1]");
  // git clone --depth 1 -b release/*******/main-sst2/20231012 https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/JproBackendRepo
  let returnCode = await executProcess(
    "git",
    ["clone", "--depth", "1", "-b", args.backend, "https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/JproBackendRepo"],
    workFolder
  );
  console.timeEnd("[1]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[2] JproBackendRepo レポジトリ (${args.backend} ブランチ) のサブモジュールをチェックアウトします。`);
  console.time("[2]");
  // git submodule update --init --recommend-shallow --depth 1  JproBackCommon
  returnCode = await executProcess(
    "git",
    ["submodule", "update", "--init", "--recommend-shallow", "--depth", "1", "JproBackCommon"],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend")
  );
  console.timeEnd("[2]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[3] dotnet パッケージをインストールします。`);
  console.time("[3]");
  // dotnet restore
  returnCode = await executProcess(
    "dotnet",
    ["restore"],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend\\JproBackCommon")
  );
  console.timeEnd("[3]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[4] バックエンドビルドを実行します。`);
  console.time("[4]");
  // dotnet build --configuration Release
  returnCode = await executProcess(
    "dotnet",
    ["build", "--configuration", "Release"],    
    path.join(workFolder, ".\\JproBackendRepo\\JproBackend\\JproBackCommon")
  );
  console.timeEnd("[4]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[5] JproBackendRepo レポジトリ (${args.service} ブランチ) をチェックアウトします。`);
  console.time("[5]");
  // git clone --depth 1 -b develop/sc/main-sst1  https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/JproDeploymentService
  returnCode = await executProcess(
    "git",
    ["clone", "--depth", "1", "-b", args.service, "https://<EMAIL>/JPRO-SYSTEM/JPRO/_git/JproDeploymentService"],
    workFolder
  );
  console.timeEnd("[5]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[6] dotnet パッケージをインストールします。`);
  console.time("[6]");
  // dotnet restore
  returnCode = await executProcess(
    "dotnet",
    ["restore"],    
    path.join(workFolder, ".\\JproDeploymentService\\JproDeploymentService")
  );
  console.timeEnd("[6]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[7] JPROリリース機能のビルドを実行します。`);
  console.time("[7]");
  // dotnet build --configuration Release
  returnCode = await executProcess(
    "dotnet",
    ["build", "--configuration", "Release"],    
    path.join(workFolder, ".\\JproDeploymentService\\JproDeploymentService")
  );
  console.timeEnd("[7]");
  if (returnCode !== 0) {
    return RETURN_CODE_COMMAND_ERROR;
  }

  console.log(`[8] ビルド結果を抽出します。`);
  console.time("[8]");
  moveJproDeploymentServiceOutputFiles();
  console.timeEnd("[8]");

  console.log(`[9] リリース資材を生成します。`);
  console.time("[9]");
  await archiveJproDeploymentServiceZip();
  console.timeEnd("[9]");

  console.log(`リリース資材の生成が完了しました。`);
  console.timeEnd();

  return RETURN_CODE_SUCCESS;
}

/**
 * メイン処理
 */
 async function execute() {
  // コマンドライン引数を解析する
  const args = minimist(process.argv.slice(2));

  // 引数を検証する
  if (!testArgs(args)) {
    return RETURN_CODE_INVALID_ARGMENT;
  }

  console.log("リリース資材生成処理を開始します");
  
  const mode = args._[0];
  workFolder = path.join(args.workfolder, crypto.randomUUID().replace(/-/g, ""));
  
  if (mode === "jpro" && !_.isEmpty(args.branch)) {
    console.log("生成対象: " + chalk.yellow("JproRelease.zip"));
    console.log("実行モード: " + chalk.yellow("JproReleaseモード"));
    console.log("JproReleaseブランチ: " + chalk.yellow(args.branch));
    console.log("出力先フォルダ: " + chalk.yellow(args.workfolder));
    // 処理
  } else if (mode === "jpro") {
    console.log("生成対象: " + chalk.yellow("JproRelease.zip"));
    console.log("実行モード: " + chalk.yellow("直接指定モード"));
    console.log("リリースバージョン: " + chalk.yellow(args.version));
    console.log("jpro-frontend-repoブランチ: " + chalk.yellow(args.frontend));
    console.log("JproBackendRepoブランチ: " + chalk.yellow(args.backend));
    console.log("出力先フォルダ: " + chalk.yellow(args.workfolder));
    // 処理
    return executeJproDirectMode(args);
  } else {
    console.log("生成対象: " + chalk.yellow("JproDeploymentService.zip"));
    console.log("jproDeploymentServiceブランチ: " + chalk.yellow(args.service));
    console.log("JproBackendRepoブランチ: " + chalk.yellow(args.backend));
    console.log("出力先フォルダ: " + chalk.yellow(args.workfolder));
    // 処理
    return executeJproDeploymentMode(args);
  }

  // ローカルAPIは正しくアップデートされました。
  return RETURN_CODE_SUCCESS;
}

/**
 * メイン処理のドライバ
 */
 async function main() {
  // メイン処理を実行する
  const result = await execute();
  // 最終処理
  try {
    // ワークフォルダを削除する
    if (result === RETURN_CODE_SUCCESS) {
      deleteWorkFolder();
    }
  } catch(error) {
    // 終了処理でエラーが起きても、それを処理結果には反映しない
    console.error(error);
  }  
  
  return result;
}

// メイン処理を起動する
main().then(result => {
  returnCode = result;
}).catch(result => {
  console.error(result);
  // ローカルAPIの更新に失敗しました (汎用エラー): 1
  returnCode = RETURN_CODE_SYSTEM_ERROR;
});

// 終了コードの設定
process.on("exit", function() {
  process.chdir(__dirname);
  process.exit(returnCode);
});