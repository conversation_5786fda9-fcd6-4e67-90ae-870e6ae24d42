const path = require('path')
const webpack = require('webpack')
const CopyFilePlugin = require("copy-webpack-plugin");
const WriteFilePlugin = require("write-file-webpack-plugin");

module.exports = {
  entry: {
    server: './src/index.js',
  },
  output: {
    path: path.join(__dirname, 'dist'),
    publicPath: '/',
    filename: 'JproDeploymentTool.js'
  },
  target: 'node',
  node: {
    __dirname: false,
    __filename: false,
  },
  resolve: {
    modules: ['node_modules'],
  },
  module: {
    rules: []
  },
  plugins: [
    new CopyFilePlugin({
      patterns:[
        { from: "./src/dotnet-framework-restore.bat" },
        { from: "./src/nuget.exe" },
        { from: "./src/jpro-frontend-build.bat" },
      ]
    }),
    new WriteFilePlugin()
  ]
}