﻿// <copyright file="EnvironmentUtility.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Util
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Runtime.CompilerServices;
    using System.ServiceProcess;
    using JproBackend.JproBackCommon.Common.Base.Dao.TableExtension;
    using JproBackend.JproBackCommon.Common.Base.Database.ChozaiDbContext;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproBackend.JproBackCommon.Common.Utility.FileAccess;
    using JproDeploymentService.Common.Entity;
    using Microsoft.Data.SqlClient;
    using Serilog;

    /// <summary>
    /// 環境情報ユーティリティクラス.
    /// インストール済みの環境情報を取得します.
    /// </summary>
    internal class EnvironmentUtility
    {
        /// <summary>
        /// <see cref="EnvironmentUtility"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="configuration">アプリ設定.</param>
        /// <param name="logger">ロガー.</param>
        public EnvironmentUtility(JproReleaseConfiguration configuration, FileLogger<EnvironmentUtility> logger = null)
        {
            this.Configuration = configuration;
            this.Logger = logger;
        }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<EnvironmentUtility> Logger { get; }

        /// <summary>
        /// 最新Windowsサービスバージョン取得処理.
        /// 現在の最新JPROサービスインストールバージョン情報を取得する.
        /// </summary>
        /// <returns>最大のインストールバージョン.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public AppVersion GetJproServiceVersion()
        {
            // サービス情報を取得する.
            List<(BackendService Servive, AppVersion Version)> serviceVersion = this.GetJproBackendServices();

            // JproBackend のサービスが存在しない場合
            if (!serviceVersion.Any())
            {
                return null;
            }

            // 最大のバージョンを応答する
            return serviceVersion.OrderByDescending(sv => sv.Version)
                .First()
                .Version;
        }

        /// <summary>
        /// 最新配置バージョン取得処理.
        /// 現在の最新JPRO展開済みバージョン情報を取得する.
        /// </summary>
        /// <returns>最大のインストールバージョン.</returns>
        public AppVersion GetJproPlacedVersion()
        {
            List<(string Path, AppVersion Version)> installVersion = this.GetJproBackendInstallPath();

            // JproBackend のサービスが存在しない場合
            if (!installVersion.Any())
            {
                return null;
            }

            // 最大のバージョンを応答する
            return installVersion.OrderByDescending(sv => sv.Version)
                .First()
                .Version;
        }

        /// <summary>
        /// CommandLine制御機能とバージョン一覧取得処理.
        /// システムに登録されているJPROバックエンドサービスの一覧を取得する.
        /// </summary>
        /// <returns>JPROバックエンドサービスの一覧 (Service: BackendServiceオブジェクト, AppVersion: アプリバージョン情報).</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは CommandLine を想定した機能")]
        public List<(BackendService Servive, AppVersion Version)> GetJproBackendServices()
        {
            // JproBackend から始まるサービス名の一覧を取得
            var backendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend");
            var backendService = Directory.GetDirectories(backendPath)
                            .Select(x => new BackendService
                            {
                                ProcessName = $"JproBackend_{Path.GetFileName(x).Replace('.', '_')}",
                            })
                            .ToList();
            foreach (var item in backendService)
            {
                Process[] process = Process.GetProcessesByName(item.ProcessName);
                item.Status = process.Any();
            }

            // デバッグ用にログ
            backendService.ForEach(service =>
                this.LogDebug($"表示名：{service.ProcessName} サービス名：{service.ProcessName} 状態: {(service.Status ? "Running" : "Stopped")}"));

            // サービス名 (JproBackend_v00_00_00_00) からバージョンを取得する.
            return backendService.Select(service => (
                    Servive: service,
                    Version: AppVersion.From(service.ProcessName, "_", "v")))
                .Where(sv => sv.Version is not null)
                .ToList();
        }

        /// <summary>
        /// インストールディレクトリとバージョン一覧取得処理.
        /// インストールフォルダに配置されているJPROバックエンドサービスの一覧を取得する.
        /// </summary>
        /// <returns>JPROバックエンドサービスの一覧 (Path: インストール先, AppVersion: アプリバージョン情報).</returns>
        public List<(string Path, AppVersion Version)> GetJproBackendInstallPath()
        {
            var backendPath = Path.Combine(this.Configuration.Deployment.JproBaseDirectory, "Backend");
            if (!DirectoryAccessor.Exists(backendPath))
            {
                return new ();
            }

            // JproBackend から始まるディレクトリ名の一覧を取得
            var installPath = Directory.GetDirectories(backendPath).ToList();

            // デバッグ用にログ
            installPath.ForEach(path => this.LogDebug($"インストールパス：{path}"));

            // サービス名 (JproBackend_v00_00_00_00) からバージョンを取得する.
            return installPath.Select(path => (
                    Path: path,
                    Version: AppVersion.From(Path.GetFileName(path), ".", "v")))
                .Where(sv => sv.Version is not null)
                .ToList();
        }

        /// <summary>
        /// 未使用ポート取得処理.
        /// 次に利用可能なJPROバックエンドサービスのポート番号を取得する.
        /// </summary>
        /// <returns>ポート番号.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public int? GetUnusedPort()
        {
            try
            {
                // 利用可能なポートの一覧
                var portRange = GetPortRange();

                // すべての登録済みサービス
                var services = this.GetJproBackendServices();

                // すべてのインストールパス
                var installPath = this.GetJproBackendInstallPath();

                this.LogInformation($"割り当てポート一覧: {string.Join(",", portRange)}");

                // 使用中のポート
                var usingPorts = services.Where(service =>
                        service.Servive.Status && // 起動中のサービス
                        installPath.Exists(path => path.Version == service.Version))
                    .Select(service =>
                    {
                        // 各サービスの設定ファイルパスを取得
                        var workPath = installPath.Find(path => path.Version == service.Version);
                        var appSettingPath = Path.Combine(workPath.Path, ReleaseConstants.AppsettingsFileName);

                        // ファイルがない場合、無視
                        if (!FileAccessor.Exists(appSettingPath))
                        {
                            return null;
                        }

                        // サービスの設定ファイルを参照してポート番号を取得する
                        var appSettingJson = new JsonFile(appSettingPath);
                        if (appSettingJson.GetValue<string>("Kestrel.Endpoints.Http.Url") is not string url)
                        {
                            return null;
                        }

                        var uri = new Uri(url);
                        var port = (int?)uri.Port;

                        // 利用中のポートをログに記録
                        this.LogInformation($"利用中のポート {port} ({service.Servive.ProcessName}) ");
                        return port;
                    })
                    .Where(port => port.HasValue)
                    .ToList();

                // 使用されていないポートのうち最小のポートを採用する
                return portRange.Where(port => !usingPorts.Contains(port))
                    .OrderBy(port => port)
                    .First();
            }
            catch (Exception exception)
            {
                this.LogError(exception, "ポートの取得に失敗しました。");
                return null;
            }

            // SysShopIni の JPRO バックエンドサーバーポート範囲を取得する
            int[] GetPortRange()
            {
                var portSetting = DbStaticCache.SysShopIni.GetCurrentSettings()
                    .First(ini => ini.DataDiv == ReleaseConstants.DataDivBackendPortRange);

                var portRangeText = portSetting.CharValue.Split("-");
                var portRange = (FromPort: int.Parse(portRangeText[0]), ToPort: int.Parse(portRangeText[1]));

                return Enumerable.Range(portRange.FromPort, portRange.ToPort - portRange.FromPort + 1).ToArray();
            }
        }

        /// <summary>
        /// 使用中ポートとバージョン一覧取得処理.
        /// インストール済みのサービスのバージョンとポートの組み合わせを作成する.
        /// </summary>
        /// <returns>(version: バージョン,  port: ポート番号).</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public List<(AppVersion version, int port)> GetServiceApiPort()
        {
            // すべての登録済みサービス
            var services = this.GetJproBackendServices();

            // すべてのインストールパス
            var installPath = this.GetJproBackendInstallPath();

            // 使用中のポート
            return services.Where(service => installPath.Exists(path => path.Version == service.Version))
                .Select(service =>
                {
                    // 各サービスの設定ファイルパスを取得
                    var workPath = installPath.Find(path => path.Version == service.Version);
                    var appSettingPath = Path.Combine(workPath.Path, ReleaseConstants.AppsettingsFileName);

                    // ファイルがない場合、無視
                    if (!FileAccessor.Exists(appSettingPath))
                    {
                        return null;
                    }

                    // サービスの設定ファイルを参照してポート番号を取得する
                    var appSettingJson = new JsonFile(appSettingPath);
                    if (appSettingJson.GetValue<string>("Kestrel.Endpoints.Http.Url") is not string url)
                    {
                        return null;
                    }

                    return new
                    {
                        version = service.Version,
                        port = new Uri(url).Port,
                    };
                })
                .Where(servicePort => servicePort is not null)
                .Select(servicePort => (version: servicePort.version, port: servicePort.port))
                .ToList();
        }

        /// <summary>
        /// Windowsサービス制御機能(JPROリリース機能)とバージョン一覧取得処理.
        /// システムに登録されているJPROリリース機能サービスの一覧を取得する.
        /// </summary>
        /// <returns>JPROリリース機能サービスの一覧 (Service: サービスオブジェクト, AppVersion: アプリバージョン情報).</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:プラットフォームの互換性を検証", Justification = "本システムは Windows を想定した機能")]
        public List<(ServiceController Servive, AppVersion Version)> GetJproDeploymentServiceVersions()
        {
            // JproBackend から始まるサービス名の一覧を取得
            var backendService = ServiceController.GetServices()
                .Where(service => service.ServiceName.StartsWith("JproDeploymentService"))
                .ToList();

            // デバッグ用にログ
            backendService.ForEach(service =>
                this.LogDebug($"表示名：{service.DisplayName} サービス名：{service.ServiceName} 状態: {service.Status}"));

            // サービス名 (JproDeploymentService_v00_00_00_00) からバージョンを取得する.
            return backendService.Select(service => (
                    Servive: service,
                    Version: AppVersion.From(service.ServiceName, "_", "v")))
                .Where(sv => sv.Version is not null)
                .ToList();
        }

        /// <summary>
        /// システム店舗情報(SYS_SHOP_INI)の手動更新処理.
        /// SysShopIni のキャッシュデータを手動更新する.
        /// </summary>
        public void RefreshIniTableCache()
        {
            try
            {
                using var sqlConnection = new SqlConnection(this.Configuration.Database.ChozaiDbConnectionString);
                sqlConnection.Open();

                var sysShopIniStatement = @"
        SELECT [DATA_DIV]
          ,[START_DATE]
          ,[LAST_DATE]
          ,[ESTABLISH_NAME]
          ,[FIELD_NAME]
          ,[CHAR_VALUE]
          ,[REAL_VALUE]
          ,[INT_VALUE]
          ,[FLAG_VALUE]
          ,[DATETIME_VALUE]
          ,[MONEY_VALUE]
        FROM [dbo].[SYS_SHOP_INI]";

                using var sysShopIniCommand = sqlConnection.CreateCommand();
                sysShopIniCommand.CommandText = sysShopIniStatement;

                using var sysShopIniReader = sysShopIniCommand.ExecuteReader();
                DbStaticCache.SysShopIni = sysShopIniReader.Cast<IDataRecord>()
                    .Select(record => new SysShopIni
                    {
                        DataDiv = record.GetInt16(0),
                        StartDate = record.GetDateTime(1),
                        LastDate = record.GetDateTime(2),
                        EstablishName = record.GetString(3),
                        FieldName = record.GetString(4),
                        CharValue = record.GetNullOrString(5),
                        RealValue = record.GetNullOrValue(6, record.GetFloat),
                        IntValue = record.GetNullOrValue(7, record.GetInt32),
                        FlagValue = record.GetNullOrValue(8, record.GetByte),
                        DatetimeValue = record.GetNullOrValue(9, record.GetDateTime),
                        MoneyValue = record.GetNullOrValue(10, record.GetDecimal),
                    })
                    .ToList();

                sysShopIniReader.Close();

                var sysCommonIniStatement = @"
        SELECT [DATA_DIV]
              ,[START_DATE]
              ,[LAST_DATE]
              ,[ESTABLISH_NAME]
              ,[FIELD_NAME]
              ,[CHAR_VALUE]
              ,[REAL_VALUE]
              ,[INT_VALUE]
              ,[FLAG_VALUE]
              ,[DATETIME_VALUE]
              ,[MONEY_VALUE]
         FROM [dbo].[SYS_COMMON_INI]";

                using var sysCommonIniCommand = sqlConnection.CreateCommand();
                sysCommonIniCommand.CommandText = sysCommonIniStatement;

                using var sysCommonIniReader = sysCommonIniCommand.ExecuteReader();
                DbStaticCache.SysCommonIni = sysCommonIniReader.Cast<IDataRecord>()
                    .Select(record => new SysCommonIni
                    {
                        DataDiv = record.GetInt16(0),
                        StartDate = record.GetDateTime(1),
                        LastDate = record.GetDateTime(2),
                        EstablishName = record.GetString(3),
                        FieldName = record.GetString(4),
                        CharValue = record.GetNullOrString(5),
                        RealValue = record.GetNullOrValue(6, record.GetFloat),
                        IntValue = record.GetNullOrValue(7, record.GetInt32),
                        FlagValue = record.GetNullOrValue(8, record.GetByte),
                        DatetimeValue = record.GetNullOrValue(9, record.GetDateTime),
                        MoneyValue = record.GetNullOrValue(10, record.GetDecimal),
                    })
                    .ToList();
            }
            catch (Exception exception)
            {
                this.LogError(exception, "SysShopIni SysCommonIni のキャッシュデータの取得に失敗しました。");
            }
        }

        /// <summary>
        /// 店舗名取得処理.
        /// 店舗名と店舗コードの情報を取得する.
        /// </summary>
        /// <returns>(店舗名, 店舗コード).</returns>
        public (string pharmacyName, string pharmacyCd) GetPharmacyData()
        {
            var pharmacyCd = DbStaticCache.SysShopIni.GetCurrentSettings()
                .First(ini => ini.DataDiv == ReleaseConstants.DataDivPharmacyCd)
                .CharValue;

            using var sqlConnection = new SqlConnection(this.Configuration.Database.ChozaiDbConnectionString);
            sqlConnection.Open();

            var statement = @"
        SELECT [PHAR_NAME]
         FROM [dbo].[PHARMACY]
        WHERE [PHAR_CODE] = @pharmacyCd";

            using var command = sqlConnection.CreateCommand();
            command.CommandText = statement;
            command.Parameters.Add(new SqlParameter("@pharmacyCd", pharmacyCd));

            using var reader = command.ExecuteReader();
            var pharmacyName = reader.Cast<IDataRecord>()
                .Select(record => record.GetString(0))
                .First();

            return (System.Text.RegularExpressions.Regex.Replace(pharmacyName, @"^日本調剤\s+", string.Empty).Trim(), pharmacyCd);
        }

        /// <summary>
        /// 動作確認用に情報ログ出力する.
        /// 自己インストールモードでロガーが存在しない場合、既定のロガーで出力する.
        /// </summary>
        /// <param name="message">ログに記録するデバッグメッセージ.</param>
        /// <param name="memberName">(この引数はメッセージパラメータではありません) コンパイラが引数名を付与します.</param>
        /// <param name="filePath">(この引数はメッセージパラメータではありません) コンパイラがファイルパスを付与します.</param>
        /// <param name="lineNumber">(この引数はメッセージパラメータではありません) コンパイラが行番号を付与します.</param>
        private void LogInformation(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (this.Logger is not null)
            {
                this.Logger.InformationLog(message, null, memberName, filePath, lineNumber);
            }
            else
            {
                Log.Debug(message);
            }
        }

        /// <summary>
        /// 動作確認用にデバッグログ出力する.
        /// 自己インストールモードでロガーが存在しない場合、既定のロガーで出力する.
        /// </summary>
        /// <param name="message">ログに記録するデバッグメッセージ.</param>
        /// <param name="memberName">(この引数はメッセージパラメータではありません) コンパイラが引数名を付与します.</param>
        /// <param name="filePath">(この引数はメッセージパラメータではありません) コンパイラがファイルパスを付与します.</param>
        /// <param name="lineNumber">(この引数はメッセージパラメータではありません) コンパイラが行番号を付与します.</param>
        private void LogDebug(
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (this.Logger is not null)
            {
                this.Logger.DebugLog(message, null, memberName, filePath, lineNumber);
            }
            else
            {
                Log.Debug(message);
            }
        }

        /// <summary>
        /// 動作確認用にエラーログ出力する.
        /// 自己インストールモードでロガーが存在しない場合、既定のロガーで出力する.
        /// </summary>
        /// <param name="exception">例外.</param>
        /// <param name="message">ログに記録するデバッグメッセージ.</param>
        /// <param name="memberName">(この引数はメッセージパラメータではありません) コンパイラが引数名を付与します.</param>
        /// <param name="filePath">(この引数はメッセージパラメータではありません) コンパイラがファイルパスを付与します.</param>
        /// <param name="lineNumber">(この引数はメッセージパラメータではありません) コンパイラが行番号を付与します.</param>
        private void LogError(
            Exception exception,
            string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNumber = 0)
        {
            if (this.Logger is not null)
            {
                this.Logger.ErrorLog(exception, message, null, memberName, filePath, lineNumber);
            }
            else
            {
                Log.Error(exception, message);
            }
        }
    }
}
