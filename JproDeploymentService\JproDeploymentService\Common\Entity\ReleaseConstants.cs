﻿// <copyright file="ReleaseConstants.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Common.Entity
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// JPROリリース機能で使用する定数定義の一覧.
    /// </summary>
    internal static class ReleaseConstants
    {
        /// <summary>
        /// SysShopIni の 店舗コードのデータ区分.
        /// </summary>
        internal const int DataDivPharmacyCd = -1;

        /// <summary>
        /// SysShopIni の アンインストールまでの待機時間のデータ区分.
        /// </summary>
        internal const int DataDivUnistallDelayTime = -624;

        /// <summary>
        /// SysShopIni の JPRO バックエンドサーバーポート範囲のデータ区分.
        /// </summary>
        internal const int DataDivBackendPortRange = -625;

        /// <summary>
        /// SysCommoIni の JPRO バックエンドユーザー名のデータ区分.
        /// </summary>
        internal const int DataDivBackendUserName = 309;

        /// <summary>
        /// SysCommonIni の JPRO バックエンドパスワードのデータ区分.
        /// </summary>
        internal const int DataDivBackendPassword = 310;

        /// <summary>
        /// インストール資材名(JPRO).
        /// </summary>
        internal const string MaterialFileNameJproRelease = "JproRelease.zip";

        /// <summary>
        /// インストール資材名Metadata(JPRO).
        /// </summary>
        internal const string MaterialFileNameJproReleaseMetadata = "JproRelease_metadata.json";

        /// <summary>
        /// インストール資材名(JPROリリース機能).
        /// </summary>
        internal const string MaterialFileNameJproDeploymentService = "JproDeploymentService.zip";

        /// <summary>
        /// バージョン管理ファイル名.
        /// </summary>
        internal const string JproVersionFileName = "JproRelease.json";

        /// <summary>
        /// 設定ファイル名.
        /// </summary>
        internal const string AppsettingsFileName = "appsettings.Production.json";

        /// <summary>
        /// 正規表現のデフォルトタイムアウト秒設定の既定値.
        /// </summary>
        internal const int RegexTimeoutEtcSetting = 2;

        /// <summary>
        /// サービス終了時のタイムアウト秒設定の既定値.
        /// </summary>
        internal const int FinalizeTimeoutEtcSetting = 15;

        /// <summary>
        /// 静的なデータベースクエリキャッシュの保持方式設定の既定値.
        /// Polling: ポーリング処理
        /// SqlDependency: クエリ通知(SqlDependency).
        /// </summary>
        internal const string StaticCacheEtcSetting = "SqlDependency";

        /// <summary>
        /// データベース(APP_VERSION_CONTRORL)が更新されたときのイベント発生猶予期間(秒)設定の既定値.
        /// </summary>
        internal const int DbDebounceIntervalEtcSetting = 10;

        /// <summary>
        /// 配信先フォルダが更新されたときのイベント発生猶予期間(秒)設定の既定値.
        /// </summary>
        internal const int FileDebounceIntervalEtcSetting = 10;

        /// <summary>
        /// 空きポートチェック処理のリトライ回数設定の既定値.
        /// </summary>
        internal const int UnusedPortRetryCountEtcSetting = 3;

        /// <summary>
        /// 空きポートチェック処理のリトライ時間(秒)設定の既定値.
        /// </summary>
        internal const int UnusedPortRetryIntervalEtcSetting = 900;

        /// <summary>
        /// 自己インストール機能の遅延開始時刻(秒)設定の既定値.
        /// </summary>
        internal const int SeflInstallTaskDelayEtcSetting = 60 * 3;

        /// <summary>
        /// インストールタスク時に失敗したテンポラリのファイルを保持する設定の既定値.
        ///  true の場合、保持する. false　の場合、保持しない.
        /// </summary>
        internal const bool PreserveMaterialsEtcSetting = false;

        /// <summary>
        /// ファイル監視のリトライ回数設定の既定値.
        /// </summary>
        internal const int FileRetryCountEtcSetting = 5;

        /// <summary>
        /// ファイル監視のリトライ初期間隔(秒)設定の既定値.
        /// </summary>
        internal const int FileRetryInitialIntervalEtcSetting = 5;

        /// <summary>
        /// ファイル監視のリトライ最大間隔(秒)設定の既定値.
        /// </summary>
        internal const int FileRetryMaxIntervalEtcSetting = 60;
    }
}
