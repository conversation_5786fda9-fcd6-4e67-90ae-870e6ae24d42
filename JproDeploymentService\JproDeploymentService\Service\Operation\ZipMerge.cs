﻿// <copyright file="ZipMerge.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>
namespace JproDeploymentService.Service.Operation
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Security.Cryptography;
    using System.Text.RegularExpressions;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Utility.DirectoryAccess;
    using JproBackend.JproBackCommon.Common.Utility.FileAccess;
    using JproDeploymentService.Common.Entity;
    using Newtonsoft.Json;

    /// <summary>
    /// Zip結合するクラス.
    /// </summary>
    public class ZipMerge
    {
        /// <summary>
        /// metadataファイルのパスの引数.
        /// </summary>
        private string metadataFilePath;

        /// <summary>
        /// inputフォルダのパスの引数.
        /// </summary>
        private string inputFolderPath;

        /// <summary>
        /// outputファイルのパスの引数.
        /// </summary>
        private string outputFilePath;

        private FileLogger<ZipMerge> Logger { get; }

        /// <summary>
        /// <see cref="ZipMerge"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="metadataFilePath">metadataファイルのパスの引数.</param>
        /// <param name="inputFolderPath">inputファイルのパスの引数.</param>
        /// <param name="outputFilePath">outputファイルのパスの引数..</param>
        public ZipMerge(string metadataFilePath, string inputFolderPath, string outputFilePath, FileLogger<ZipMerge> logger)
        {
            this.metadataFilePath = metadataFilePath;
            this.inputFolderPath = inputFolderPath;
            this.outputFilePath = outputFilePath;
            this.Logger = logger;
        }

        /// <summary>
        /// metaDataファイルの処理.
        /// </summary>
        /// <param name="metadataFilePath">metaDataファイルのパス.</param>
        /// <returns> Dictionary.</returns>
        public List<Dictionary<string, string>> ReadMetaData(string metadataFilePath)
        {
            string jsonContent = File.ReadAllText(metadataFilePath);
            var lst = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(jsonContent);
            return lst ?? new List<Dictionary<string, string>>();
        }

        public string CalculateMD5(string filePath)
        {
            using (var md5 = MD5.Create())
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var hash = md5.ComputeHash(stream);
                    return BitConverter.ToString(hash).Replace("-", string.Empty).ToLowerInvariant();
                }
            }
        }

        public bool CompareHashMD5(string metadataFilePath, List<string> inputFilePaths)
        {
            var filesList = this.ReadMetaData(metadataFilePath);
            for (var i = 0; i < inputFilePaths.Count; i++)
            {
                if (this.CalculateMD5(inputFilePaths[i]) != filesList[i]["checksum"])
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// ファイルを結合するメソッド.
        /// </summary>
        public string MergeFiles()
        {
            string pattern = @"\.z\d+";
            var outputFileExist = Directory.GetFiles(this.inputFolderPath)
                                           .Where(path => Regex.IsMatch(path, ReleaseConstants.MaterialFileNameJproRelease)).ToList();
            if (outputFileExist.Count > 0)
            {
                return this.outputFilePath;
            }
            // metadataファイルが存在するか確認。存在しなかったら、5秒ほど待ちます。
            if (!FileAccessor.Exists(this.metadataFilePath))
            {
                // throw new FileNotFoundException($"{this.metadataFilePath}存在しないファイルです。");
                this.Logger.InformationLog($"{this.metadataFilePath}存在しないファイルです。");
                return null;
            }

            try
            {
                // 正しい順序を確保するために、入力フォルダー内のすべてのファイルを名前でソートして取得します。
                var filesList = this.ReadMetaData(this.metadataFilePath);
                var inputFilePaths = Directory.GetFiles(this.inputFolderPath)
                                        .Where(path => Regex.IsMatch(path, pattern))
                                        .OrderBy(path =>
                                        {
                                            // ファイル拡張子を取得
                                            var extension = Path.GetExtension(path);

                                            // 形式が常に「.z<number>」であると仮定して、「.z」部分を削除して数値を取得します
                                            var numericPart = extension.Substring(2);

                                            // 数値を正しく並べ替えるために数値部分を整数に変換します
                                            if (int.TryParse(numericPart, out int number))
                                            {
                                                return number;
                                            }
                                            else
                                            {
                                                // 予期される形式と一致しないファイルを最後に配置して処理します
                                                return int.MaxValue;
                                            }
                                        })
                                        .ToList();
                if (filesList.Count == inputFilePaths.Count && this.CompareHashMD5(this.metadataFilePath, inputFilePaths))
                {
                    using (var outputStream = new FileStream(this.outputFilePath, FileMode.Create))
                    {
                        foreach (var inputFilePath in inputFilePaths)
                        {
                            // 必要に応じて、より高度なフィルタリングをここに追加できます
                            if (!FileAccessor.Exists(inputFilePath))
                            {
                                this.Logger.InformationLog($"{inputFilePath}存在しないファイルです。");
                                throw new FileNotFoundException($"{inputFilePath}存在しないファイルです。");
                            }

                            using (var inputStream = new FileStream(inputFilePath, FileMode.Open))
                            {
                                inputStream.CopyTo(outputStream);
                            }
                        }
                    }
                    this.Logger.InformationLog($"マージが正常に完了しました。 出力ファイル:{this.outputFilePath}");
                    return this.outputFilePath;
                }
                else
                {
                    this.Logger.InformationLog($"アップロード中：{inputFilePaths.Count}/{filesList.Count}またはmd5 文字列が等しくないです。");
                    return null;
                }
            }
            catch (Exception ex)
            {
                this.Logger.InformationLog($"マージプロセス中にエラーが発生しました:{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 各ファイルを削除するメソッド.
        /// </summary>
        /// <param name="folderPath">フォルダのパス.</param>
        /// <param name="ignoreFileName">無視されたファイル名.</param>
        public void RemoveFiles(string folderPath, string ignoreFileName)
        {
            if (!DirectoryAccessor.Exists(folderPath))
            {
                throw new DirectoryNotFoundException($"{folderPath} 存在しないディレクトリです。");
            }
            else
            {
                DirectoryInfo dir_info = new DirectoryInfo(folderPath);
                foreach (FileInfo file in dir_info.GetFiles())
                {
                    if (!file.Name.Equals(ignoreFileName, StringComparison.OrdinalIgnoreCase))
                    {
                        file.Delete();
                    }
                }

                foreach (DirectoryInfo dir in dir_info.GetDirectories())
                {
                    dir.Delete(true);
                }
            }
        }
    }
}
