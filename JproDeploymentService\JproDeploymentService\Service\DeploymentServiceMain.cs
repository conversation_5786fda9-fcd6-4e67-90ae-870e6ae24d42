﻿// <copyright file="DeploymentServiceMain.cs" company="日本調剤">
// Copyright (c) 日本調剤. All rights reserved.
// </copyright>

namespace JproDeploymentService.Service
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using JproBackend.JproBackCommon.Common.Base.Service;
    using JproBackend.JproBackCommon.Common.Base.Utility;
    using JproDeploymentService.Common.Entity;
    using JproDeploymentService.SelfInstaller;
    using JproDeploymentService.Service.Operation;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// メインロジッククラス.
    /// JPROリリース機能のメインロジック.
    /// </summary>
    internal class DeploymentServiceMain : BackgroundService
    {
        /// <summary>
        /// <see cref="DeploymentServiceMain"/> クラスの新しいインスタンスを初期化します.
        /// </summary>
        /// <param name="loggerFactory">ログファクトリ.</param>
        /// <param name="messageProvider">メッセージ取得機能.</param>
        /// <param name="environment">環境情報.</param>
        public DeploymentServiceMain(
            ILoggerFactory loggerFactory,
            IMessageProvider messageProvider,
            IHostEnvironment environment)
        {
            this.Configuration = InternalActivator.CreateConfiguration(environment.EnvironmentName, false)
                .ToJproReleaseConfiguration();

            this.LoggerFactory = loggerFactory;
            this.MessageProvider = messageProvider;
            this.Logger = this.SupplyLogger<DeploymentServiceMain>();
            this.Environment = environment;
        }

        /// <summary>
        /// キー/値形式の設定情報を取得する.
        /// </summary>
        private JproReleaseConfiguration Configuration { get; set; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private FileLogger<DeploymentServiceMain> Logger { get; }

        /// <summary>
        /// ロガーを取得する.
        /// </summary>
        private ILoggerFactory LoggerFactory { get; }

        /// <summary>
        /// ホスト環境情報を取得する.
        /// </summary>
        private IHostEnvironment Environment { get; }

        /// <summary>
        /// メッセージ取得クラスを取得する.
        /// </summary>
        private IMessageProvider MessageProvider { get; }

        /// <summary>
        /// 現在実行中のインストールタスク(InstallTask)クラス.
        /// </summary>
        private InstallTask CurrentInstallTask { get; set; }

        /// <summary>
        /// 現在実行中のインストールタスク(InstallTask)クラスのキャンセルトークンソース.
        /// このトークンを経由して現在実行中のインストールタスクを強制停止する.
        /// </summary>
        private CancellationTokenSource CurrentCancellationTokenSource { get; set; }

        /// <summary>
        /// その他のタスク(アンインストールタスク(UninstallTask)や自己インストールタスク(SelfInstallerTask))のキャンセルトークンソース.
        /// このトークンを経由して現在実行中のアンインストールタスクや自己インストールタスクを強制停止する.
        /// </summary>
        private CancellationTokenSource OtherTaskCancellationTokenSource { get; set; }

        /// <summary>
        /// JPROのインストールタスクとJRPOリリース機能の自己インストールタスクを排他制御するためのシグナル.
        /// </summary>
        private SemaphoreSlim InstallSignal { get; set; }

        /// <summary>
        /// サービスが停止中かどうか判別するためのフラグ.
        /// true の場合、サービスは停止中である. false の場合、サービスは停止中でない.
        /// </summary>
        private bool ServiceStopping { get; set; }

        /// <summary>
        /// メインロジック開始処理.
        /// JPROリリース機能が開始するときのイベントハンドラ.
        /// </summary>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>このメソッドは非同期処理.</returns>
        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            // アンインストール処理と自己インストール処理をキャンセルするためのトークンソースを生成する
            this.OtherTaskCancellationTokenSource = new CancellationTokenSource();

            // 以前の起動処理時に削除されていないサービスやディレクトリを削除する
            await this.ExecuteInitalClearanceTask();

            // インストール処理中にJPROのインストールタスクとJRPOリリース機能の自己インストールタスクを排他制御するためのシグナル
            this.InstallSignal = new SemaphoreSlim(1);

            // 自己インストール処理を開始する
            _ = Task.Run(() => this.ExecuteSelfInstallerTask());

            await base.StartAsync(cancellationToken);
        }

        /// <summary>
        /// メインロジック終了処理.
        /// JPROリリース機能が停止するときのイベントハンドラ.
        /// </summary>
        /// <param name="cancellationToken">キャンセルトークン.</param>
        /// <returns>このメソッドは非同期処理.</returns>
        public override Task StopAsync(CancellationToken cancellationToken)
        {
            this.ServiceStopping = true;

            // サービスを停止する
            this.CurrentCancellationTokenSource?.Cancel();
            this.OtherTaskCancellationTokenSource.Cancel();

            // メインタスクの終了を待機する
            var isTimeout = this.CurrentInstallTask?.WaitFinalize(this.Configuration.Etc.FinalizeTimeout);
            if (isTimeout.HasValue && !isTimeout.Value)
            {
                this.Logger.WarningLog("サーバーの停止処理など外部の操作によってJPROリリース機能は中断停止されました。タイムアウトのため強制終了されます。");
            }

            return base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// メインロジック処理.
        /// JPROリリース機能のメインロジック.
        /// </summary>
        /// <param name="stoppingToken">キャンセルトークン.</param>
        /// <returns>このメソッドは非同期処理.</returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested && !this.ServiceStopping)
            {
                bool terminatedByError = false;
                try
                {
                    // 今回のタスクで利用するリソースを確保
                    this.CurrentCancellationTokenSource = new ();
                    this.CurrentInstallTask = new InstallTask(
                        this.LoggerFactory,
                        this.Environment,
                        this.MessageProvider);

                    // 処理を開始
                    var cancekToken = this.CurrentCancellationTokenSource.Token;
                    var version = await this.CurrentInstallTask.Execute(cancekToken, this.InstallSignal);

                    if (version is not null)
                    {
                        this.Logger.InformationLog($"JPROのインストールに成功しました。インストールバージョン: {version}");

                        // インストールバージョン情報を出力
                        this.OutputDeploymentResult(version);

                        // インストールに成功した場合、古いバージョンのサービスをアンインストールする
                        _ = Task.Run(() => this.UninstallPreviousService());
                    }
                }
                catch (Exception exception)
                {
                    // システムが停止された場合、
                    if (exception is not OperationCanceledException)
                    {
                        this.Logger.ErrorLog(exception, "JPROリリース機能の実行中にエラーが発生しました。");

                        // 進行中のイベントはすべてキャンセルする
                        this.CurrentCancellationTokenSource?.Cancel();

                        // エラーが原因でインストールタスクが終了した場合、ループで待機を行う
                        terminatedByError = true;
                    }
                    else
                    {
                        // 想定通りのタスク停止処理
                        this.Logger.DebugLog("インストールタスクはキャンセルで終了されました。");
                    }
                }
                finally
                {
                    // リソース破棄
                    this.CurrentCancellationTokenSource?.Dispose();
                    this.CurrentInstallTask?.Dispose();

                    // 排他制御シグナルをリリース
                    this.InstallSignal.Release();

                    // 参照削除
                    this.CurrentCancellationTokenSource = null;
                    this.CurrentInstallTask = null;
                }

                // エラーでループが終了した場合、一定時間待機する
                if (terminatedByError)
                {
                    await this.StandyAfterError();
                }
            }
        }

        /// <summary>
        /// アンインストールタスク起動処理.
        /// 古いバージョンを使用不可にします.
        /// </summary>
        /// <param name="uninstallImmediately">直ぐに古いバージョンを使用不可にする.</param>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        private async Task UninstallPreviousService(bool uninstallImmediately = false)
        {
            var uninstallTask = new UninstallTask(this.LoggerFactory, this.Environment, this.MessageProvider);
            await uninstallTask.Execute(this.OtherTaskCancellationTokenSource.Token, uninstallImmediately);
        }

        /// <summary>
        /// 自己インストールタスク起動処理.
        /// JPROリリース機能の自己インストール処理のメイン処理.
        /// </summary>
        /// <returns>このメソッドは非同期処理.</returns>
        private async Task ExecuteSelfInstallerTask()
        {
            // JPROのインストール処理(3分後)から遅れて開始する
            await Task.Delay(this.Configuration.Etc.SeflInstallTaskDelay * 1000);

            while (!this.OtherTaskCancellationTokenSource.IsCancellationRequested && !this.ServiceStopping)
            {
                try
                {
                    using var installer = new SelfInstallerTask(this.LoggerFactory, this.Environment, this.MessageProvider);
                    await installer.Execute(this.OtherTaskCancellationTokenSource.Token, this.InstallSignal);
                }
                catch (Exception exception)
                {
                    if (exception is not OperationCanceledException)
                    {
                        this.Logger.ErrorLog(exception, "JPROリリース機能の自己インストール処理でエラーが発生しました。");
                        await this.StandyAfterError();
                    }
                }
            }
        }

        /// <summary>
        /// エラーリトライ待機処理.
        /// エラー後、指定期間待機する.
        /// </summary>
        /// <returns>この処理は戻り値のない非同期処理.</returns>
        private async Task StandyAfterError()
        {
            try
            {
                // 待機中にサービスが終了する場合、このトークンで待機を終了させる
                this.CurrentCancellationTokenSource = new CancellationTokenSource();

                var retryInterval = this.Configuration.Deployment.ErrorRetryInterval;
                this.Logger.InformationLog($"{retryInterval} 秒間待機します。");
                await Task.Delay(retryInterval * 1000, this.CurrentCancellationTokenSource.Token);
            }
            finally
            {
                this.CurrentCancellationTokenSource = null;
            }
        }

        /// <summary>
        /// サービス起動時のディレクトリ整備処理
        /// - 過去のJPROリリース機能を即時モードで削除する.
        /// - 過去のリリース処理で削除されずに残っている不要な一時フォルダを削除する.
        /// </summary>
        /// <returns>この処理は戻り値のない非同期タスク.</returns>
        private async Task ExecuteInitalClearanceTask()
        {
            try
            {
                this.Logger.InformationLog("サービス起動時のディレクトリ整備処理を開始します。");

                // 過去のJPROリリース機能を即時モードで削除する
                await this.UninstallPreviousService(true);

                // 過去のリリース処理で削除されずに残っている不要な一時フォルダを削除する
                var fileOperation = this.CreateFileOperation();

                fileOperation.DeleteReleaseTempDir();

                this.Logger.InformationLog("サービス起動時のディレクトリ整備処理を終了します。");
            }
            catch (Exception exception)
            {
                this.Logger.ErrorLog(exception, "サービス起動時のディレクトリ整備処理でエラーが発生しました");
            }
        }

        /// <summary>
        /// バージョン情報ログ出力処理.
        /// JPROのインストールバージョンをログ出力する.
        /// </summary>
        /// <param name="version">インストールバージョン.</param>
        private void OutputDeploymentResult(AppVersion version)
        {
            try
            {
                var fileOperation = this.CreateFileOperation();
                fileOperation.OutputDeploymentResult(version);
            }
            catch (Exception exception)
            {
                this.Logger.ErrorLog(exception, "リリース結果出力ファイルの生成に失敗しました。");
            }
        }

        /// <summary>
        /// ファイルロガーを生成する.
        /// </summary>
        /// <typeparam name="TCategory">ロガーのカテゴリクラス.</typeparam>
        /// <returns>ファイルロガー.</returns>
        private FileLogger<TCategory> SupplyLogger<TCategory>()
        {
            return FileLogger<TCategory>.CreateFileLogger(
                this.LoggerFactory.CreateLogger<TCategory>(),
                this.MessageProvider);
        }

        /// <summary>
        /// ファイル処理操作クラス生成処理.
        /// </summary>
        /// <returns>ファイル操作機能.</returns>
        private FileOperation CreateFileOperation()
        {
            return new FileOperation(
                this.SupplyLogger<FileOperation>(),
                this.Configuration);
        }
    }
}
